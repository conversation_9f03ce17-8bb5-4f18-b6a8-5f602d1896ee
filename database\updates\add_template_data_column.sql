-- Add template_data column to layout_templates table for storing drawings and groups
-- This column will store JSON data for canvas drawings and element groups

-- Check if the column already exists
SET @dbname = DATABASE();
SET @tablename = "layout_templates";
SET @columnname = "template_data";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = @tablename)
      AND (COLUMN_NAME = @columnname)
  ) > 0,
  "SELECT 'Column template_data already exists in layout_templates table.'",
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " LONGTEXT NULL AFTER background_color;")
));

PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Also need to fix the template_elements table column names to match what the code expects
-- The database has x_position, y_position but the code expects x, y

SET @columnname1 = "x";
SET @preparedStatement1 = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = "template_elements")
      AND (COLUMN_NAME = @columnname1)
  ) > 0,
  "SELECT 'Column x already exists in template_elements table.'",
  "ALTER TABLE template_elements CHANGE COLUMN x_position x INT NOT NULL;"
));

PREPARE alterIfNotExists1 FROM @preparedStatement1;
EXECUTE alterIfNotExists1;
DEALLOCATE PREPARE alterIfNotExists1;

SET @columnname2 = "y";
SET @preparedStatement2 = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = "template_elements")
      AND (COLUMN_NAME = @columnname2)
  ) > 0,
  "SELECT 'Column y already exists in template_elements table.'",
  "ALTER TABLE template_elements CHANGE COLUMN y_position y INT NOT NULL;"
));

PREPARE alterIfNotExists2 FROM @preparedStatement2;
EXECUTE alterIfNotExists2;
DEALLOCATE PREPARE alterIfNotExists2;
