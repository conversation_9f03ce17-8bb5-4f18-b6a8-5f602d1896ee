apple-device-compatibility.js:18 🍎 Apple Device Compatibility: Initializing...
apple-device-compatibility.js:19 🍎 Is Apple Device: false
apple-device-compatibility.js:20 🍎 Is iOS Safari: false
apple-device-compatibility.js:21 🍎 Is PWA Standalone: false
apple-device-compatibility.js:22 🍎 Is iPad: false
apple-device-compatibility.js:23 🍎 Screen dimensions: 1920x919
apple-device-compatibility.js:24 🍎 Device pixel ratio: 1
fcm-notifications.js:16 [FCM] fcm-notifications.js loaded
fcm-notifications.js:497 [FCM] FCMNotificationManager class made globally available
9:2761 Element before parsing: 201 show_parking properties: {"category":"157","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 201 {category: '157', vehicle_capacity: 50}
9:2773 Element after parsing: 201 show_parking properties: {category: '157', vehicle_capacity: 50}
9:2761 Element before parsing: 220 building properties: {"description":"Building","floors":1} type: string
9:2765 Parsed properties for element: 220 {description: 'Building', floors: 1}
9:2773 Element after parsing: 220 building properties: {description: 'Building', floors: 1}
9:2761 Element before parsing: 202 show_parking properties: {"category":"152","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 202 {category: '152', vehicle_capacity: 50}
9:2773 Element after parsing: 202 show_parking properties: {category: '152', vehicle_capacity: 50}
9:2761 Element before parsing: 216 show_parking properties: {"category":"155","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 216 {category: '155', vehicle_capacity: 50}
9:2773 Element after parsing: 216 show_parking properties: {category: '155', vehicle_capacity: 50}
9:2761 Element before parsing: 217 entrance properties: {"capacity":null,"description":"Main entrance"} type: string
9:2765 Parsed properties for element: 217 {capacity: null, description: 'Main entrance'}
9:2773 Element after parsing: 217 entrance properties: {capacity: null, description: 'Main entrance'}
9:2761 Element before parsing: 208 food_area properties: {"vendor_count":3} type: string
9:2765 Parsed properties for element: 208 {vendor_count: 3}
9:2773 Element after parsing: 208 food_area properties: {vendor_count: 3}
9:2761 Element before parsing: 224 food_area properties: {"vendor_count":3} type: string
9:2765 Parsed properties for element: 224 {vendor_count: 3}
9:2773 Element after parsing: 224 food_area properties: {vendor_count: 3}
9:2761 Element before parsing: 222 judging_area properties: {"description":"Vehicle judging area"} type: string
9:2765 Parsed properties for element: 222 {description: 'Vehicle judging area'}
9:2773 Element after parsing: 222 judging_area properties: {description: 'Vehicle judging area'}
9:2761 Element before parsing: 207 show_parking properties: {"category":"159","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 207 {category: '159', vehicle_capacity: 50}
9:2773 Element after parsing: 207 show_parking properties: {category: '159', vehicle_capacity: 50}
9:2761 Element before parsing: 203 show_parking properties: {"category":"160","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 203 {category: '160', vehicle_capacity: 50}
9:2773 Element after parsing: 203 show_parking properties: {category: '160', vehicle_capacity: 50}
9:2761 Element before parsing: 205 show_parking properties: {"category":"153","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 205 {category: '153', vehicle_capacity: 50}
9:2773 Element after parsing: 205 show_parking properties: {category: '153', vehicle_capacity: 50}
9:2761 Element before parsing: 209 restroom properties: {"facilities":["mens","womens"]} type: string
9:2765 Parsed properties for element: 209 {facilities: Array(2)}
9:2773 Element after parsing: 209 restroom properties: {facilities: Array(2)}
9:2761 Element before parsing: 225 restroom properties: {"facilities":["mens","womens"]} type: string
9:2765 Parsed properties for element: 225 {facilities: Array(2)}
9:2773 Element after parsing: 225 restroom properties: {facilities: Array(2)}
9:2761 Element before parsing: 210 stage properties: {"description":"Performance area"} type: string
9:2765 Parsed properties for element: 210 {description: 'Performance area'}
9:2773 Element after parsing: 210 stage properties: {description: 'Performance area'}
9:2761 Element before parsing: 204 show_parking properties: {"category":"158","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 204 {category: '158', vehicle_capacity: 50}
9:2773 Element after parsing: 204 show_parking properties: {category: '158', vehicle_capacity: 50}
9:2761 Element before parsing: 199 vendor_area properties: {"booth_count":10,"booth_size":"10x10"} type: string
9:2765 Parsed properties for element: 199 {booth_count: 10, booth_size: '10x10'}
9:2773 Element after parsing: 199 vendor_area properties: {booth_count: 10, booth_size: '10x10'}
9:2761 Element before parsing: 200 vendor_area properties: {"booth_count":10,"booth_size":"10x10"} type: string
9:2765 Parsed properties for element: 200 {booth_count: 10, booth_size: '10x10'}
9:2773 Element after parsing: 200 vendor_area properties: {booth_count: 10, booth_size: '10x10'}
9:2761 Element before parsing: 223 vendor_area properties: {"booth_count":10,"booth_size":"10x10"} type: string
9:2765 Parsed properties for element: 223 {booth_count: 10, booth_size: '10x10'}
9:2773 Element after parsing: 223 vendor_area properties: {booth_count: 10, booth_size: '10x10'}
9:2761 Element before parsing: 206 vendor_booth properties: {"booth_size":"10x10","power":false,"table":false} type: string
9:2765 Parsed properties for element: 206 {booth_size: '10x10', power: false, table: false}
9:2773 Element after parsing: 206 vendor_booth properties: {booth_size: '10x10', power: false, table: false}
9:2761 Element before parsing: 211 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 211 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 211 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:2761 Element before parsing: 212 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 212 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 212 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:2761 Element before parsing: 213 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":true} type: string
9:2765 Parsed properties for element: 213 {vehicle_capacity: 30, handicap_accessible: true}
9:2773 Element after parsing: 213 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: true}
9:2761 Element before parsing: 214 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":true} type: string
9:2765 Parsed properties for element: 214 {vehicle_capacity: 30, handicap_accessible: true}
9:2773 Element after parsing: 214 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: true}
9:2761 Element before parsing: 226 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 226 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 226 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:2761 Element before parsing: 227 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 227 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 227 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:9567 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1759178228:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759178228:500 CameraBanner loaded - version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759178228:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759178228:54 CameraBanner: About to load site logo
apple-device-compatibility.js:35 🍎 Not an Apple device, skipping Apple-specific enhancements
header-settings.js:85 📱 Header Settings: Initializing...
header-settings.js:115 📱 Header Settings: Loading from https://events.rowaneliterides.com/home/<USER>
fcm-notifications.js:459 [FCM] DOM loaded, checking initialization conditions...
fcm-notifications.js:463 [FCM] User logged in, initializing FCM manager...
fcm-notifications.js:23 [FCM] FCMNotificationManager constructor called
fcm-notifications.js:47 [FCM] Support check: {serviceWorker: true, pushManager: true, notification: true, isIOS: false, supported: true}
fcm-notifications.js:36 [FCM] FCM Notification Manager initialized
fcm-notifications.js:37 [FCM] Is supported: true
fcm-notifications.js:38 [FCM] Debug mode: false
main.js:10 [Main] DOM loaded, initializing...
main.js:16 [Main] CSRF token found
main.js:20 [Main] Main.js initialization complete
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js?v=1759178228:1158 [NotificationManager] DOM ready, initializing...
notifications.js?v=1759178228:1161 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1759178228:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1759178228:33 [PWA] Initializing PWA features...
camera-banner.js?v=1759178228:508 CameraBanner: Starting initialization...
9:9636 [PWA] PWA features initialized successfully
pwa-features.js?v=1759178228:115 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1759178228:116 [PWA] Caching disabled - all content will be fetched fresh
pwa-features.js?v=1759178228:204 [PWA] IndexedDB initialized
pwa-features.js?v=1759178228:364 [PWA] Push notifications handled by FCM system (fcm-notifications.js)
pwa-features.js?v=1759178228:59 [PWA] About to check for forced update...
pwa-features.js?v=1759178228:2835 [PWA] Checking forced update flags: {forceUpdate: null, cacheCleared: null, showNotification: null, forceUpdateAfterClear: null, cookieForceUpdate: 'not found'}
pwa-features.js?v=1759178228:64 [PWA] PWA features initialized
pwa-features.js?v=1759178228:70 [PWA] Setting up update banner check in 3 seconds...
header-settings.js:118 📱 Header Settings: Response status 200
camera-banner.js?v=1759178228:57 CameraBanner: About to load banners
header-settings.js:122 📱 Header Settings: Raw response {success: true, settings: {…}, timestamp: 1759178229}
header-settings.js:126 📱 Header Settings: Loaded from controller {sticky_header_desktop: '0', sticky_header_ipad_portrait: '0', sticky_header_ipad_landscape: '0', sticky_header_android_portrait: '0', sticky_header_android_landscape: '0', …}
header-settings.js:162 📱 Header Settings: Device Detection {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:163 📱 Header Settings: Applying {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:281 📱 Header Settings: Sticky header disabled (forced)
header-settings.js:307 📱 Header Settings: Orientation handling setup
notification-center.js?v=1759178228:148 [NotificationCenter] Desktop bell reset to normal
camera-banner.js?v=1759178228:86 Fetch finished loading: GET "https://events.rowaneliterides.com/api/getSiteLogo".
loadSiteLogo @ camera-banner.js?v=1759178228:86
init @ camera-banner.js?v=1759178228:55
(anonymous) @ camera-banner.js?v=1759178228:36
setTimeout
CameraBanner @ camera-banner.js?v=1759178228:35
(anonymous) @ camera-banner.js?v=1759178228:499
header-settings.js:117 Fetch finished loading: GET "https://events.rowaneliterides.com/home/<USER>".
loadHeaderSettings @ header-settings.js:117
initHeaderSettings @ header-settings.js:88
notification-center.js?v=1759178228:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178228:69
init @ notification-center.js?v=1759178228:42
NotificationCenter @ notification-center.js?v=1759178228:15
(anonymous) @ notification-center.js?v=1759178228:362
camera-banner.js?v=1759178228:119 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759178228:119
initializeBannerSystem @ camera-banner.js?v=1759178228:509
notifications.js?v=1759178228:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178228:554
init @ notifications.js?v=1759178228:69
NotificationManager @ notifications.js?v=1759178228:46
(anonymous) @ notifications.js?v=1759178228:1160
camera-banner.js?v=1759178228:119 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759178228:119
init @ camera-banner.js?v=1759178228:58
await in init
(anonymous) @ camera-banner.js?v=1759178228:36
setTimeout
CameraBanner @ camera-banner.js?v=1759178228:35
(anonymous) @ camera-banner.js?v=1759178228:499
camera-banner.js?v=1759178228:137 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759178228:137
await in loadBanners
initializeBannerSystem @ camera-banner.js?v=1759178228:509
camera-banner.js?v=1759178228:60 CameraBanner: Initialization complete, banners loaded: 5
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
camera-banner.js?v=1759178228:137 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759178228:137
await in loadBanners
init @ camera-banner.js?v=1759178228:58
await in init
(anonymous) @ camera-banner.js?v=1759178228:36
setTimeout
CameraBanner @ camera-banner.js?v=1759178228:35
(anonymous) @ camera-banner.js?v=1759178228:499
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notifications.js?v=1759178228:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178228:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ desktop-notifications-fix.js:173
setTimeout
enhanceNotificationManager @ desktop-notifications-fix.js:171
checkForManager @ desktop-notifications-fix.js:114
waitForNotificationManager @ desktop-notifications-fix.js:122
initDesktopFix @ desktop-notifications-fix.js:38
(anonymous) @ desktop-notifications-fix.js:209
fcm-notifications.js:468 [FCM] Starting delayed initialization...
fcm-notifications.js:63 [FCM] Initializing Firebase...
fcm-notifications.js:71 [FCM] Firebase SDK version: 9.23.0
fcm-notifications.js:114 [FCM] Ensuring unified service worker is registered...
fcm-notifications.js:127 [FCM] Unified service worker already registered: https://events.rowaneliterides.com/
fcm-notifications.js:148 [FCM] Unified service worker is active and ready
fcm-notifications.js:78 [FCM] Initializing Firebase app...
fcm-notifications.js:94 [FCM] Using unified service worker for Firebase messaging: https://events.rowaneliterides.com/
fcm-notifications.js:99 [FCM] Firebase messaging initialized with unified service worker
fcm-notifications.js:158 [FCM] Getting VAPID key...
fcm-notifications.js:161 Fetch finished loading: GET "https://events.rowaneliterides.com/api/pwa/vapid-key".
getVAPIDKey @ fcm-notifications.js:161
initializeFirebase @ fcm-notifications.js:102
await in initializeFirebase
(anonymous) @ fcm-notifications.js:473
setTimeout
(anonymous) @ fcm-notifications.js:467
fcm-notifications.js:166 [FCM] VAPID key obtained
fcm-notifications.js:104 [FCM] Firebase initialized successfully
fcm-notifications.js:253 [FCM] Setting up foreground message handling...
fcm-notifications.js:478 [FCM] Permission already granted, getting token...
fcm-notifications.js:178 [FCM] requestPermissionAndGetToken called
fcm-notifications.js:283 [FCM] Checking cached token: exists
fcm-notifications.js:306 [FCM] Cached token is valid
fcm-notifications.js:189 [FCM] Using cached FCM token
fcm-notifications.js:329 Fetch finished loading: POST "https://events.rowaneliterides.com/api/pwa/fcm-verify-token".
verifyTokenInDatabase @ fcm-notifications.js:329
getCachedToken @ fcm-notifications.js:299
requestPermissionAndGetToken @ fcm-notifications.js:187
(anonymous) @ fcm-notifications.js:479
setTimeout
(anonymous) @ fcm-notifications.js:467
pwa-features.js?v=1759178228:829 [PWA] Found 0 FAB camera buttons
pwa-features.js?v=1759178228:72 [PWA] 3 seconds elapsed, checking for update banner flag now...
pwa-features.js?v=1759178228:2918 [PWA] Checking for update banner flag...
pwa-features.js?v=1759178228:2921 [PWA] Update banner flag: null
pwa-features.js?v=1759178228:2932 [PWA] No update banner flag found
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759178228:148 [NotificationCenter] Desktop bell reset to normal
notification-center.js?v=1759178228:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178228:69
(anonymous) @ notification-center.js?v=1759178228:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759178228:177
init @ notification-center.js?v=1759178228:45
NotificationCenter @ notification-center.js?v=1759178228:15
(anonymous) @ notification-center.js?v=1759178228:362
notifications.js?v=1759178228:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178228:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759178228:77
setInterval
init @ notifications.js?v=1759178228:76
NotificationManager @ notifications.js?v=1759178228:46
(anonymous) @ notifications.js?v=1759178228:1160
9:8061 Showing kids area properties for element: {id: 'new_1', layout_id: 1, element_type: 'kids_area', name: 'Kids Area', x_position: 426.828125, …}
9:8971 Saving layout elements: (27) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
9:8974 Saving element: new_1 kids_area properties: {kids_area_type: 'playground'}
9:8974 Saving element: new_2 registration_area properties: {registration_type: 'show_participants'}
9:8985 Fetch finished loading: POST "https://events.rowaneliterides.com/layout_manager/saveLayout".
saveLayout @ 9:8985
onclick @ 9:2128
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759178228:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178228:69
(anonymous) @ notification-center.js?v=1759178228:208
notification-center.js?v=1759178228:148 [NotificationCenter] Desktop bell reset to normal
notifications.js?v=1759178228:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178228:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759178228:77
setInterval
init @ notifications.js?v=1759178228:76
NotificationManager @ notifications.js?v=1759178228:46
(anonymous) @ notifications.js?v=1759178228:1160
notification-center.js?v=1759178228:148 [NotificationCenter] Desktop bell reset to normal
notification-center.js?v=1759178228:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178228:69
(anonymous) @ notification-center.js?v=1759178228:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759178228:177
init @ notification-center.js?v=1759178228:45
NotificationCenter @ notification-center.js?v=1759178228:15
(anonymous) @ notification-center.js?v=1759178228:362
notification-center.js?v=1759178228:148 [NotificationCenter] Desktop bell reset to normal
notification-center.js?v=1759178228:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178228:69
(anonymous) @ notification-center.js?v=1759178228:208
Fetch failed loading: POST "https://events.rowaneliterides.com/api/pwa/usage".
apple-device-compatibility.js:18 🍎 Apple Device Compatibility: Initializing...
apple-device-compatibility.js:19 🍎 Is Apple Device: false
apple-device-compatibility.js:20 🍎 Is iOS Safari: false
apple-device-compatibility.js:21 🍎 Is PWA Standalone: false
apple-device-compatibility.js:22 🍎 Is iPad: false
apple-device-compatibility.js:23 🍎 Screen dimensions: 1920x919
Navigated to https://events.rowaneliterides.com/layout_manager/designer/9
apple-device-compatibility.js:24 🍎 Device pixel ratio: 1
fcm-notifications.js:16 [FCM] fcm-notifications.js loaded
fcm-notifications.js:497 [FCM] FCMNotificationManager class made globally available
9:2761 Element before parsing: 201 show_parking properties: {"category":"157","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 201 {category: '157', vehicle_capacity: 50}
9:2773 Element after parsing: 201 show_parking properties: {category: '157', vehicle_capacity: 50}
9:2761 Element before parsing: 220 building properties: {"description":"Building","floors":1} type: string
9:2765 Parsed properties for element: 220 {description: 'Building', floors: 1}
9:2773 Element after parsing: 220 building properties: {description: 'Building', floors: 1}
9:2761 Element before parsing: 202 show_parking properties: {"category":"152","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 202 {category: '152', vehicle_capacity: 50}
9:2773 Element after parsing: 202 show_parking properties: {category: '152', vehicle_capacity: 50}
9:2761 Element before parsing: 216 show_parking properties: {"category":"155","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 216 {category: '155', vehicle_capacity: 50}
9:2773 Element after parsing: 216 show_parking properties: {category: '155', vehicle_capacity: 50}
9:2761 Element before parsing: 217 entrance properties: {"capacity":null,"description":"Main entrance"} type: string
9:2765 Parsed properties for element: 217 {capacity: null, description: 'Main entrance'}
9:2773 Element after parsing: 217 entrance properties: {capacity: null, description: 'Main entrance'}
9:2761 Element before parsing: 208 food_area properties: {"vendor_count":3} type: string
9:2765 Parsed properties for element: 208 {vendor_count: 3}
9:2773 Element after parsing: 208 food_area properties: {vendor_count: 3}
9:2761 Element before parsing: 224 food_area properties: {"vendor_count":3} type: string
9:2765 Parsed properties for element: 224 {vendor_count: 3}
9:2773 Element after parsing: 224 food_area properties: {vendor_count: 3}
9:2761 Element before parsing: 222 judging_area properties: {"description":"Vehicle judging area"} type: string
9:2765 Parsed properties for element: 222 {description: 'Vehicle judging area'}
9:2773 Element after parsing: 222 judging_area properties: {description: 'Vehicle judging area'}
9:2761 Element before parsing: 230  properties: {"kids_area_type":"playground"} type: string
9:2765 Parsed properties for element: 230 {kids_area_type: 'playground'}
9:2773 Element after parsing: 230  properties: {kids_area_type: 'playground'}
9:2761 Element before parsing: 207 show_parking properties: {"category":"159","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 207 {category: '159', vehicle_capacity: 50}
9:2773 Element after parsing: 207 show_parking properties: {category: '159', vehicle_capacity: 50}
9:2761 Element before parsing: 203 show_parking properties: {"category":"160","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 203 {category: '160', vehicle_capacity: 50}
9:2773 Element after parsing: 203 show_parking properties: {category: '160', vehicle_capacity: 50}
9:2761 Element before parsing: 205 show_parking properties: {"category":"153","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 205 {category: '153', vehicle_capacity: 50}
9:2773 Element after parsing: 205 show_parking properties: {category: '153', vehicle_capacity: 50}
9:2761 Element before parsing: 231  properties: {"registration_type":"show_participants"} type: string
9:2765 Parsed properties for element: 231 {registration_type: 'show_participants'}
9:2773 Element after parsing: 231  properties: {registration_type: 'show_participants'}
9:2761 Element before parsing: 209 restroom properties: {"facilities":["mens","womens"]} type: string
9:2765 Parsed properties for element: 209 {facilities: Array(2)}
9:2773 Element after parsing: 209 restroom properties: {facilities: Array(2)}
9:2761 Element before parsing: 225 restroom properties: {"facilities":["mens","womens"]} type: string
9:2765 Parsed properties for element: 225 {facilities: Array(2)}
9:2773 Element after parsing: 225 restroom properties: {facilities: Array(2)}
9:2761 Element before parsing: 210 stage properties: {"description":"Performance area"} type: string
9:2765 Parsed properties for element: 210 {description: 'Performance area'}
9:2773 Element after parsing: 210 stage properties: {description: 'Performance area'}
9:2761 Element before parsing: 204 show_parking properties: {"category":"158","vehicle_capacity":50} type: string
9:2765 Parsed properties for element: 204 {category: '158', vehicle_capacity: 50}
9:2773 Element after parsing: 204 show_parking properties: {category: '158', vehicle_capacity: 50}
9:2761 Element before parsing: 199 vendor_area properties: {"booth_count":10,"booth_size":"10x10"} type: string
9:2765 Parsed properties for element: 199 {booth_count: 10, booth_size: '10x10'}
9:2773 Element after parsing: 199 vendor_area properties: {booth_count: 10, booth_size: '10x10'}
9:2761 Element before parsing: 200 vendor_area properties: {"booth_count":10,"booth_size":"10x10"} type: string
9:2765 Parsed properties for element: 200 {booth_count: 10, booth_size: '10x10'}
9:2773 Element after parsing: 200 vendor_area properties: {booth_count: 10, booth_size: '10x10'}
9:2761 Element before parsing: 223 vendor_area properties: {"booth_count":10,"booth_size":"10x10"} type: string
9:2765 Parsed properties for element: 223 {booth_count: 10, booth_size: '10x10'}
9:2773 Element after parsing: 223 vendor_area properties: {booth_count: 10, booth_size: '10x10'}
9:2761 Element before parsing: 206 vendor_booth properties: {"booth_size":"10x10","power":false,"table":false} type: string
9:2765 Parsed properties for element: 206 {booth_size: '10x10', power: false, table: false}
9:2773 Element after parsing: 206 vendor_booth properties: {booth_size: '10x10', power: false, table: false}
9:2761 Element before parsing: 211 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 211 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 211 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:2761 Element before parsing: 212 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 212 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 212 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:2761 Element before parsing: 213 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":true} type: string
9:2765 Parsed properties for element: 213 {vehicle_capacity: 30, handicap_accessible: true}
9:2773 Element after parsing: 213 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: true}
9:2761 Element before parsing: 214 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":true} type: string
9:2765 Parsed properties for element: 214 {vehicle_capacity: 30, handicap_accessible: true}
9:2773 Element after parsing: 214 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: true}
9:2761 Element before parsing: 226 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 226 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 226 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:2761 Element before parsing: 227 visitor_parking properties: {"vehicle_capacity":30,"handicap_accessible":false} type: string
9:2765 Parsed properties for element: 227 {vehicle_capacity: 30, handicap_accessible: false}
9:2773 Element after parsing: 227 visitor_parking properties: {vehicle_capacity: 30, handicap_accessible: false}
9:9567 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1759178309:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759178309:500 CameraBanner loaded - version: 3.64.0-cache-bust-fix
apple-device-compatibility.js:35 🍎 Not an Apple device, skipping Apple-specific enhancements
header-settings.js:85 📱 Header Settings: Initializing...
header-settings.js:115 📱 Header Settings: Loading from https://events.rowaneliterides.com/home/<USER>
fcm-notifications.js:459 [FCM] DOM loaded, checking initialization conditions...
fcm-notifications.js:463 [FCM] User logged in, initializing FCM manager...
fcm-notifications.js:23 [FCM] FCMNotificationManager constructor called
fcm-notifications.js:47 [FCM] Support check: {serviceWorker: true, pushManager: true, notification: true, isIOS: false, supported: true}
fcm-notifications.js:36 [FCM] FCM Notification Manager initialized
fcm-notifications.js:37 [FCM] Is supported: true
fcm-notifications.js:38 [FCM] Debug mode: false
main.js:10 [Main] DOM loaded, initializing...
main.js:16 [Main] CSRF token found
main.js:20 [Main] Main.js initialization complete
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js?v=1759178309:1158 [NotificationManager] DOM ready, initializing...
notifications.js?v=1759178309:1161 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1759178309:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1759178309:33 [PWA] Initializing PWA features...
camera-banner.js?v=1759178309:508 CameraBanner: Starting initialization...
9:9636 [PWA] PWA features initialized successfully
camera-banner.js?v=1759178309:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759178309:54 CameraBanner: About to load site logo
pwa-features.js?v=1759178309:115 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1759178309:116 [PWA] Caching disabled - all content will be fetched fresh
pwa-features.js?v=1759178309:204 [PWA] IndexedDB initialized
pwa-features.js?v=1759178309:364 [PWA] Push notifications handled by FCM system (fcm-notifications.js)
pwa-features.js?v=1759178309:59 [PWA] About to check for forced update...
pwa-features.js?v=1759178309:2835 [PWA] Checking forced update flags: {forceUpdate: null, cacheCleared: null, showNotification: null, forceUpdateAfterClear: null, cookieForceUpdate: 'not found'}
pwa-features.js?v=1759178309:64 [PWA] PWA features initialized
pwa-features.js?v=1759178309:70 [PWA] Setting up update banner check in 3 seconds...
header-settings.js:118 📱 Header Settings: Response status 200
header-settings.js:122 📱 Header Settings: Raw response {success: true, settings: {…}, timestamp: 1759178309}
header-settings.js:126 📱 Header Settings: Loaded from controller {sticky_header_desktop: '0', sticky_header_ipad_portrait: '0', sticky_header_ipad_landscape: '0', sticky_header_android_portrait: '0', sticky_header_android_landscape: '0', …}
header-settings.js:162 📱 Header Settings: Device Detection {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:163 📱 Header Settings: Applying {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:281 📱 Header Settings: Sticky header disabled (forced)
header-settings.js:307 📱 Header Settings: Orientation handling setup
notification-center.js?v=1759178309:148 [NotificationCenter] Desktop bell reset to normal
camera-banner.js?v=1759178309:57 CameraBanner: About to load banners
header-settings.js:117 Fetch finished loading: GET "https://events.rowaneliterides.com/home/<USER>".
loadHeaderSettings @ header-settings.js:117
initHeaderSettings @ header-settings.js:88
camera-banner.js?v=1759178309:119 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759178309:119
initializeBannerSystem @ camera-banner.js?v=1759178309:509
notifications.js?v=1759178309:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178309:554
init @ notifications.js?v=1759178309:69
NotificationManager @ notifications.js?v=1759178309:46
(anonymous) @ notifications.js?v=1759178309:1160
notification-center.js?v=1759178309:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178309:69
init @ notification-center.js?v=1759178309:42
NotificationCenter @ notification-center.js?v=1759178309:15
(anonymous) @ notification-center.js?v=1759178309:362
camera-banner.js?v=1759178309:86 Fetch finished loading: GET "https://events.rowaneliterides.com/api/getSiteLogo".
loadSiteLogo @ camera-banner.js?v=1759178309:86
init @ camera-banner.js?v=1759178309:55
(anonymous) @ camera-banner.js?v=1759178309:36
setTimeout
CameraBanner @ camera-banner.js?v=1759178309:35
(anonymous) @ camera-banner.js?v=1759178309:499
camera-banner.js?v=1759178309:60 CameraBanner: Initialization complete, banners loaded: 5
camera-banner.js?v=1759178309:137 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759178309:137
await in loadBanners
initializeBannerSystem @ camera-banner.js?v=1759178309:509
camera-banner.js?v=1759178309:119 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759178309:119
init @ camera-banner.js?v=1759178309:58
await in init
(anonymous) @ camera-banner.js?v=1759178309:36
setTimeout
CameraBanner @ camera-banner.js?v=1759178309:35
(anonymous) @ camera-banner.js?v=1759178309:499
camera-banner.js?v=1759178309:137 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759178309:137
await in loadBanners
init @ camera-banner.js?v=1759178309:58
await in init
(anonymous) @ camera-banner.js?v=1759178309:36
setTimeout
CameraBanner @ camera-banner.js?v=1759178309:35
(anonymous) @ camera-banner.js?v=1759178309:499
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
fcm-notifications.js:468 [FCM] Starting delayed initialization...
fcm-notifications.js:63 [FCM] Initializing Firebase...
fcm-notifications.js:71 [FCM] Firebase SDK version: 9.23.0
fcm-notifications.js:114 [FCM] Ensuring unified service worker is registered...
fcm-notifications.js:127 [FCM] Unified service worker already registered: https://events.rowaneliterides.com/
fcm-notifications.js:148 [FCM] Unified service worker is active and ready
fcm-notifications.js:78 [FCM] Initializing Firebase app...
fcm-notifications.js:94 [FCM] Using unified service worker for Firebase messaging: https://events.rowaneliterides.com/
fcm-notifications.js:99 [FCM] Firebase messaging initialized with unified service worker
fcm-notifications.js:158 [FCM] Getting VAPID key...
fcm-notifications.js:166 [FCM] VAPID key obtained
fcm-notifications.js:104 [FCM] Firebase initialized successfully
fcm-notifications.js:253 [FCM] Setting up foreground message handling...
fcm-notifications.js:478 [FCM] Permission already granted, getting token...
fcm-notifications.js:178 [FCM] requestPermissionAndGetToken called
fcm-notifications.js:283 [FCM] Checking cached token: exists
fcm-notifications.js:306 [FCM] Cached token is valid
fcm-notifications.js:189 [FCM] Using cached FCM token
notifications.js?v=1759178309:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178309:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ desktop-notifications-fix.js:173
setTimeout
enhanceNotificationManager @ desktop-notifications-fix.js:171
checkForManager @ desktop-notifications-fix.js:114
waitForNotificationManager @ desktop-notifications-fix.js:122
initDesktopFix @ desktop-notifications-fix.js:38
(anonymous) @ desktop-notifications-fix.js:209
fcm-notifications.js:161 Fetch finished loading: GET "https://events.rowaneliterides.com/api/pwa/vapid-key".
getVAPIDKey @ fcm-notifications.js:161
initializeFirebase @ fcm-notifications.js:102
await in initializeFirebase
(anonymous) @ fcm-notifications.js:473
setTimeout
(anonymous) @ fcm-notifications.js:467
fcm-notifications.js:329 Fetch finished loading: POST "https://events.rowaneliterides.com/api/pwa/fcm-verify-token".
verifyTokenInDatabase @ fcm-notifications.js:329
getCachedToken @ fcm-notifications.js:299
requestPermissionAndGetToken @ fcm-notifications.js:187
(anonymous) @ fcm-notifications.js:479
setTimeout
(anonymous) @ fcm-notifications.js:467
pwa-features.js?v=1759178309:829 [PWA] Found 0 FAB camera buttons
pwa-features.js?v=1759178309:72 [PWA] 3 seconds elapsed, checking for update banner flag now...
pwa-features.js?v=1759178309:2918 [PWA] Checking for update banner flag...
pwa-features.js?v=1759178309:2921 [PWA] Update banner flag: null
pwa-features.js?v=1759178309:2932 [PWA] No update banner flag found
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759178309:148 [NotificationCenter] Desktop bell reset to normal
notification-center.js?v=1759178309:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178309:69
(anonymous) @ notification-center.js?v=1759178309:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759178309:177
init @ notification-center.js?v=1759178309:45
NotificationCenter @ notification-center.js?v=1759178309:15
(anonymous) @ notification-center.js?v=1759178309:362
notifications.js?v=1759178309:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178309:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759178309:77
setInterval
init @ notifications.js?v=1759178309:76
NotificationManager @ notifications.js?v=1759178309:46
(anonymous) @ notifications.js?v=1759178309:1160
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759178309:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178309:69
(anonymous) @ notification-center.js?v=1759178309:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759178309:177
init @ notification-center.js?v=1759178309:45
NotificationCenter @ notification-center.js?v=1759178309:15
(anonymous) @ notification-center.js?v=1759178309:362
notification-center.js?v=1759178309:148 [NotificationCenter] Desktop bell reset to normal
notifications.js?v=1759178309:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178309:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759178309:77
setInterval
init @ notifications.js?v=1759178309:76
NotificationManager @ notifications.js?v=1759178309:46
(anonymous) @ notifications.js?v=1759178309:1160
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759178309:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178309:69
(anonymous) @ notification-center.js?v=1759178309:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759178309:177
init @ notification-center.js?v=1759178309:45
NotificationCenter @ notification-center.js?v=1759178309:15
(anonymous) @ notification-center.js?v=1759178309:362
notification-center.js?v=1759178309:148 [NotificationCenter] Desktop bell reset to normal
notifications.js?v=1759178309:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178309:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759178309:77
setInterval
init @ notifications.js?v=1759178309:76
NotificationManager @ notifications.js?v=1759178309:46
(anonymous) @ notifications.js?v=1759178309:1160
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759178309:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759178309:69
(anonymous) @ notification-center.js?v=1759178309:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759178309:177
init @ notification-center.js?v=1759178309:45
NotificationCenter @ notification-center.js?v=1759178309:15
(anonymous) @ notification-center.js?v=1759178309:362
notification-center.js?v=1759178309:148 [NotificationCenter] Desktop bell reset to normal
notifications.js?v=1759178309:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759178309:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759178309:77
setInterval
init @ notifications.js?v=1759178309:76
NotificationManager @ notifications.js?v=1759178309:46
(anonymous) @ notifications.js?v=1759178309:1160
