<?php

require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Controller.php';
require_once APPROOT . '/core/Auth.php';
require_once APPROOT . '/helpers/timezone_helper.php';

class LayoutManagerController extends Controller {
    private $db;
    private $auth;
    
    public function __construct() {
        // Check if user is logged in and has appropriate role
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->db = new Database();
    }
    
    /**
     * Main layout management dashboard
     */
    public function index() {
        // Get all shows with layouts
        $shows = $this->getShowsWithLayouts();
        
        // Get available templates
        $templates = $this->getAvailableTemplates();
        
        // Get venues for template filtering
        $venues = $this->getVenues();
        
        require_once APPROOT . '/views/layout_manager/dashboard.php';
    }
    
    /**
     * Layout designer for a specific show
     */
    public function designer($show_id = null) {
        if (!$show_id) {
            header('Location: ' . URLROOT . '/layout_manager');
            exit;
        }
        
        // Get show details
        $show = $this->getShowById($show_id);
        if (!$show) {
            $_SESSION['error'] = 'Show not found';
            header('Location: ' . URLROOT . '/layout_manager');
            exit;
        }
        
        // Get or create layout for this show
        $layout = $this->getOrCreateShowLayout($show_id);
        
        // Get layout elements (sections, vendor areas, etc.)
        $elements = $this->getLayoutElements($layout->id);

        // Get layout data (drawings and groups)
        $layoutData = $this->getLayoutData($layout->id);
        $drawings = $layoutData['drawings'] ?? [];
        $groups = $layoutData['groups'] ?? [];

        // Get show categories for section assignment
        $categories = $this->getShowCategories($show_id);

        // Get registered vendors for assignment
        $vendors = $this->getRegisteredVendors($show_id);

        // Get available templates
        $templates = $this->getAvailableTemplates($show->venue_id ?? null);

        // Debug: Log what we're passing to the view
        error_log("Layout ID: " . $layout->id);
        error_log("Elements count: " . count($elements));
        error_log("Drawings count: " . count($drawings));
        error_log("Groups count: " . count($groups));

        require_once APPROOT . '/views/layout_manager/designer.php';
    }
    
    /**
     * Section assignment interface
     */
    public function sections($show_id = null) {
        if (!$show_id) {
            header('Location: ' . URLROOT . '/layout_manager');
            exit;
        }
        
        // Get show details
        $show = $this->getShowById($show_id);
        if (!$show) {
            $_SESSION['error'] = 'Show not found';
            header('Location: ' . URLROOT . '/layout_manager');
            exit;
        }
        
        // Get layout
        $layout = $this->getShowLayout($show_id);
        if (!$layout) {
            $_SESSION['error'] = 'No layout found for this show. Create a layout first.';
            header('Location: ' . URLROOT . '/layout_manager/designer/' . $show_id);
            exit;
        }
        
        // Get parking sections
        $parkingSections = $this->getParkingSections($layout->id);
        
        // Get vendor areas
        $vendorAreas = $this->getVendorAreas($layout->id);
        
        // Get show categories with vehicle counts
        $categories = $this->getShowCategoriesWithCounts($show_id);
        
        // Get registered vendors
        $vendors = $this->getRegisteredVendors($show_id);
        
        // Get current assignments
        $sectionAssignments = $this->getSectionAssignments($show_id);
        $vendorAssignments = $this->getVendorAssignments($show_id);
        
        require_once APPROOT . '/views/layout_manager/sections.php';
    }
    
    /**
     * Template management
     */
    public function templates() {
        // Get all templates (user's own + public)
        $templates = $this->getAllTemplates();
        
        // Get venues for template creation
        $venues = $this->getVenues();
        
        require_once APPROOT . '/views/layout_manager/templates.php';
    }
    
    /**
     * Save layout elements
     */
    public function saveLayout() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        try {
            $layout_id = $data['layout_id'];
            $elements = $data['elements'];
            $drawings = $data['drawings'] ?? [];
            $groups = $data['groups'] ?? [];

            // Update layout elements
            $this->updateLayoutElements($layout_id, $elements);

            // Update layout drawings and groups
            $this->updateLayoutData($layout_id, $drawings, $groups);

            echo json_encode(['success' => true, 'message' => 'Layout saved successfully']);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to save layout: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Assign category to parking section
     */
    public function assignSection() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        
        try {
            $section_id = $data['section_id'];
            $show_id = $data['show_id'];
            $category_id = $data['category_id'];
            $vehicle_count = $data['vehicle_count'] ?? 0;
            
            // Save section assignment
            $this->saveSectionAssignment($section_id, $show_id, $category_id, $vehicle_count);
            
            echo json_encode(['success' => true, 'message' => 'Section assigned successfully']);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to assign section: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Auto-assign vendors to available booths
     */
    public function autoAssignVendors() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        
        try {
            $show_id = $data['show_id'];
            $vendor_area_id = $data['vendor_area_id'] ?? null;
            
            $result = $this->performAutoVendorAssignment($show_id, $vendor_area_id);
            
            echo json_encode([
                'success' => true, 
                'message' => 'Auto-assignment completed',
                'assigned_count' => $result['assigned_count'],
                'total_vendors' => $result['total_vendors']
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to auto-assign vendors: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Create layout from template
     */
    public function createFromTemplate() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        try {
            $show_id = $data['show_id'];
            $template_id = $data['template_id'];

            $layout_id = $this->createLayoutFromTemplate($show_id, $template_id);

            echo json_encode([
                'success' => true,
                'message' => 'Layout created from template',
                'layout_id' => $layout_id
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create layout from template: ' . $e->getMessage()]);
        }
    }

    /**
     * Save current layout as template
     */
    public function saveTemplate() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        try {
            $name = trim($data['name']);
            $description = trim($data['description'] ?? '');
            $is_public = (int)($data['is_public'] ?? 0);
            $show_id = $data['show_id'];
            $layout_id = $data['layout_id'];
            $elements = $data['elements'] ?? [];
            $drawings = $data['drawings'] ?? [];
            $groups = $data['groups'] ?? [];
            $canvas_width = $data['canvas_width'] ?? 1200;
            $canvas_height = $data['canvas_height'] ?? 800;

            if (empty($name)) {
                throw new Exception('Template name is required');
            }

            // Get show and venue info for template context
            $sql = "SELECT s.*, cv.id as venue_id FROM shows s
                    LEFT JOIN calendar_venues cv ON s.venue_id = cv.id
                    WHERE s.id = ?";
            $this->db->query($sql);
            $this->db->bind(1, $show_id);
            $show = $this->db->single();

            if (!$show) {
                throw new Exception('Show not found');
            }

            $template_id = $this->createTemplate($name, $description, $show->venue_id, $is_public, $canvas_width, $canvas_height, $elements, $drawings, $groups);

            echo json_encode([
                'success' => true,
                'message' => 'Template saved successfully',
                'template_id' => $template_id
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to save template: ' . $e->getMessage()]);
        }
    }
    
    // Private helper methods
    
    private function getShowsWithLayouts() {
        $sql = "SELECT s.*, 
                       COUNT(DISTINCT r.id) as total_registrations,
                       COUNT(DISTINCT psa.id) as assigned_sections,
                       COUNT(DISTINCT vba.id) as assigned_vendors,
                       el.id as layout_id,
                       COALESCE(cv.name, s.location) as venue_name,
                       cv.id as venue_id
                FROM shows s
                LEFT JOIN registrations r ON s.id = r.show_id
                LEFT JOIN parking_section_assignments psa ON s.id = psa.show_id
                LEFT JOIN vendor_booth_assignments vba ON s.id = vba.show_id
                LEFT JOIN event_layouts el ON s.id = el.show_id
                LEFT JOIN calendar_venues cv ON s.venue_id = cv.id
                WHERE s.status = 'published'
                GROUP BY s.id
                ORDER BY s.start_date DESC";
        
        $this->db->query($sql);
        return $this->db->resultSet();
    }
    
    private function getShowById($show_id) {
        $sql = "SELECT s.*, 
                       COALESCE(cv.name, s.location) as venue_name,
                       cv.id as venue_id
                FROM shows s 
                LEFT JOIN calendar_venues cv ON s.venue_id = cv.id
                WHERE s.id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        return $this->db->single();
    }
    
    private function getOrCreateShowLayout($show_id) {
        // Try to get existing layout
        $layout = $this->getShowLayout($show_id);
        
        if (!$layout) {
            // Create new layout
            $sql = "INSERT INTO event_layouts (show_id, name, canvas_width, canvas_height, created_by) 
                    VALUES (?, 'Main Layout', 1200, 800, ?)";
            $this->db->query($sql);
            $this->db->bind(1, $show_id);
            $this->db->bind(2, $_SESSION['user_id']);
            $this->db->execute();
            
            $layout_id = $this->db->lastInsertId();
            
            // Get the created layout
            $sql = "SELECT * FROM event_layouts WHERE id = ?";
            $this->db->query($sql);
            $this->db->bind(1, $layout_id);
            $layout = $this->db->single();
        }
        
        return $layout;
    }
    
    private function getShowLayout($show_id) {
        $sql = "SELECT * FROM event_layouts WHERE show_id = ? LIMIT 1";
        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        return $this->db->single();
    }
    
    private function getLayoutElements($layout_id) {
        $sql = "SELECT le.*, sc.name as category_name
                FROM layout_elements le
                LEFT JOIN show_categories sc ON le.category_id = sc.id
                WHERE le.layout_id = ?
                ORDER BY le.z_index, le.name";
        
        $this->db->query($sql);
        $this->db->bind(1, $layout_id);
        return $this->db->resultSet();
    }
    
    private function getShowCategories($show_id) {
        $sql = "SELECT * FROM show_categories WHERE show_id = ? ORDER BY name";
        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        return $this->db->resultSet();
    }
    
    private function getShowCategoriesWithCounts($show_id) {
        $sql = "SELECT sc.*, COUNT(r.id) as vehicle_count
                FROM show_categories sc
                LEFT JOIN registrations r ON sc.id = r.category_id AND r.show_id = ? AND r.status = 'approved'
                WHERE sc.show_id = ?
                GROUP BY sc.id
                ORDER BY sc.name";
        
        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        $this->db->bind(2, $show_id);
        return $this->db->resultSet();
    }
    
    private function getRegisteredVendors($show_id) {
        // This would need to be implemented based on your vendor/sponsor system
        // For now, return empty array
        return [];
    }
    
    private function getParkingSections($layout_id) {
        $sql = "SELECT * FROM layout_elements 
                WHERE layout_id = ? AND element_type = 'parking_section'
                ORDER BY name";
        
        $this->db->query($sql);
        $this->db->bind(1, $layout_id);
        return $this->db->resultSet();
    }
    
    private function getVendorAreas($layout_id) {
        $sql = "SELECT * FROM layout_elements 
                WHERE layout_id = ? AND element_type IN ('vendor_area', 'vendor_booth')
                ORDER BY name";
        
        $this->db->query($sql);
        $this->db->bind(1, $layout_id);
        return $this->db->resultSet();
    }
    
    private function getSectionAssignments($show_id) {
        $sql = "SELECT psa.*, le.name as section_name, sc.name as category_name
                FROM parking_section_assignments psa
                JOIN layout_elements le ON psa.layout_element_id = le.id
                JOIN show_categories sc ON psa.category_id = sc.id
                WHERE psa.show_id = ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        return $this->db->resultSet();
    }
    
    private function getVendorAssignments($show_id) {
        $sql = "SELECT vba.*, le.name as booth_name
                FROM vendor_booth_assignments vba
                JOIN layout_elements le ON vba.layout_element_id = le.id
                WHERE vba.show_id = ?";
        
        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        return $this->db->resultSet();
    }
    
    private function getAvailableTemplates($venue_id = null) {
        $sql = "SELECT * FROM layout_templates 
                WHERE (is_public = 1 OR created_by = ?) 
                AND (venue_id IS NULL OR venue_id = ?)
                ORDER BY template_type, name";
        
        $this->db->query($sql);
        $this->db->bind(1, $_SESSION['user_id']);
        $this->db->bind(2, $venue_id);
        return $this->db->resultSet();
    }
    
    private function getAllTemplates() {
        $sql = "SELECT lt.*, cv.name as venue_name, u.name as creator_name
                FROM layout_templates lt
                LEFT JOIN calendar_venues cv ON lt.venue_id = cv.id
                LEFT JOIN users u ON lt.created_by = u.id
                WHERE lt.is_public = 1 OR lt.created_by = ?
                ORDER BY lt.template_type, lt.name";
        
        $this->db->query($sql);
        $this->db->bind(1, $_SESSION['user_id']);
        return $this->db->resultSet();
    }
    
    private function getVenues() {
        $sql = "SELECT * FROM calendar_venues ORDER BY name";
        $this->db->query($sql);
        return $this->db->resultSet();
    }
    
    private function updateLayoutElements($layout_id, $elements) {
        // Start transaction
        $this->db->beginTransaction();

        try {

        // Get existing elements
        $sql = "SELECT * FROM layout_elements WHERE layout_id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $layout_id);
        $existingElements = $this->db->resultSet();

        $existingLookup = [];
        foreach ($existingElements as $element) {
            $existingLookup[$element->id] = $element;
        }

        $processedIds = [];

        foreach ($elements as $element) {
            $elementId = null;

            if (isset($element['id']) && !str_starts_with($element['id'], 'new_')) {
                $elementId = $element['id'];
            }

            if ($elementId && isset($existingLookup[$elementId])) {
                // Update existing element
                $sql = "UPDATE layout_elements 
                        SET element_type = ?, name = ?, x_position = ?, y_position = ?, width = ?, height = ?, 
                            rotation = ?, color = ?, border_color = ?, opacity = ?, z_index = ?, properties = ?, 
                            category_id = ?, capacity = ?, handicap_accessible = ?
                        WHERE id = ?";
                
                $this->db->query($sql);
                $this->db->bind(1, $element['element_type']);
                $this->db->bind(2, $element['name']);
                $this->db->bind(3, $element['x_position']);
                $this->db->bind(4, $element['y_position']);
                $this->db->bind(5, $element['width']);
                $this->db->bind(6, $element['height']);
                $this->db->bind(7, $element['rotation'] ?? 0);
                $this->db->bind(8, $element['color'] ?? '#007bff');
                $this->db->bind(9, $element['border_color'] ?? '#dee2e6');
                $this->db->bind(10, $element['opacity'] ?? 1.0);
                $this->db->bind(11, $element['z_index'] ?? 1);
                $properties = $element['properties'] ?? [];
                if (is_string($properties)) {
                    $propertiesJson = $properties;
                } else {
                    $propertiesJson = json_encode($properties);
                }
                $this->db->bind(12, $propertiesJson);
                $this->db->bind(13, $element['category_id'] ?? null);
                $this->db->bind(14, $element['capacity'] ?? null);
                $this->db->bind(15, $element['handicap_accessible'] ?? 0);
                $this->db->bind(16, $elementId);
                $this->db->execute();
                
                $processedIds[] = $elementId;
            } else {
                // Insert new element
                $sql = "INSERT INTO layout_elements
                        (layout_id, element_type, name, x_position, y_position, width, height,
                         rotation, color, border_color, opacity, z_index, properties, category_id, capacity, handicap_accessible)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $this->db->query($sql);
                $this->db->bind(1, $layout_id);
                $this->db->bind(2, $element['element_type']);
                $this->db->bind(3, $element['name']);
                $this->db->bind(4, $element['x_position']);
                $this->db->bind(5, $element['y_position']);
                $this->db->bind(6, $element['width']);
                $this->db->bind(7, $element['height']);
                $this->db->bind(8, $element['rotation'] ?? 0);
                $this->db->bind(9, $element['color'] ?? '#007bff');
                $this->db->bind(10, $element['border_color'] ?? '#dee2e6');
                $this->db->bind(11, $element['opacity'] ?? 1.0);
                $this->db->bind(12, $element['z_index'] ?? 1);
                $properties = $element['properties'] ?? [];
                if (is_string($properties)) {
                    $propertiesJson = $properties;
                } else {
                    $propertiesJson = json_encode($properties);
                }
                $this->db->bind(13, $propertiesJson);
                $this->db->bind(14, $element['category_id'] ?? null);
                $this->db->bind(15, $element['capacity'] ?? null);
                $this->db->bind(16, $element['handicap_accessible'] ?? 0);
                $this->db->execute();
            }
        }
        
        // Delete elements that are no longer present
        // We need to preserve both existing elements that were updated AND newly inserted elements
        $allElementsToPreserve = array_merge($processedIds, []); // Start with processed existing elements

        // Add any newly inserted element IDs that we can identify
        // Note: We can't easily get the new IDs here since they're generated during insert
        // So we'll use a different approach - only delete elements that were in the original
        // existing elements list but are NOT in the current elements list

        $currentElementIds = array_column($elements, 'id');
        $existingElementIds = array_keys($existingLookup);

        // Find existing elements that are no longer in the current layout
        $elementsToDelete = [];
        foreach ($existingElementIds as $existingId) {
            $foundInCurrent = false;
            foreach ($currentElementIds as $currentId) {
                if ($currentId == $existingId) {
                    $foundInCurrent = true;
                    break;
                }
            }
            if (!$foundInCurrent) {
                $elementsToDelete[] = $existingId;
            }
        }

        // Only delete specific elements that were removed
        if (!empty($elementsToDelete)) {
            $placeholders = str_repeat('?,', count($elementsToDelete) - 1) . '?';
            $sql = "DELETE FROM layout_elements WHERE layout_id = ? AND id IN ($placeholders)";

            $this->db->query($sql);
            $this->db->bind(1, $layout_id);
            foreach ($elementsToDelete as $index => $id) {
                $this->db->bind($index + 2, $id);
            }
            $this->db->execute();
        }

        // Commit transaction
        $this->db->commit();

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    private function updateLayoutData($layout_id, $drawings, $groups) {
        // Update the layout_data field in event_layouts table with drawings and groups
        $layoutData = [
            'drawings' => $drawings,
            'groups' => $groups,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $sql = "UPDATE event_layouts SET layout_data = ? WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, json_encode($layoutData));
        $this->db->bind(2, $layout_id);
        $this->db->execute();
    }

    private function getLayoutData($layout_id) {
        $sql = "SELECT layout_data FROM event_layouts WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $layout_id);
        $result = $this->db->single();

        if ($result && $result->layout_data) {
            return json_decode($result->layout_data, true);
        }

        return ['drawings' => [], 'groups' => []];
    }

    private function saveSectionAssignment($section_id, $show_id, $category_id, $vehicle_count) {
        // Delete existing assignment for this section
        $sql = "DELETE FROM parking_section_assignments WHERE layout_element_id = ? AND show_id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $section_id);
        $this->db->bind(2, $show_id);
        $this->db->execute();
        
        // Insert new assignment
        $sql = "INSERT INTO parking_section_assignments 
                (layout_element_id, show_id, category_id, vehicle_count) 
                VALUES (?, ?, ?, ?)";
        
        $this->db->query($sql);
        $this->db->bind(1, $section_id);
        $this->db->bind(2, $show_id);
        $this->db->bind(3, $category_id);
        $this->db->bind(4, $vehicle_count);
        $this->db->execute();
        
        // Update the layout element's category_id for visual reference
        $sql = "UPDATE layout_elements SET category_id = ? WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $category_id);
        $this->db->bind(2, $section_id);
        $this->db->execute();
    }
    
    private function performAutoVendorAssignment($show_id, $vendor_area_id = null) {
        // This would implement the auto-assignment logic
        // For now, return a placeholder result
        return [
            'assigned_count' => 0,
            'total_vendors' => 0
        ];
    }
    
    private function createTemplate($name, $description, $venue_id, $is_public, $canvas_width, $canvas_height, $elements, $drawings, $groups) {
        // Create template record
        $sql = "INSERT INTO layout_templates
                (name, description, venue_id, template_type, canvas_width, canvas_height, is_public, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        $template_type = $venue_id ? 'venue_specific' : 'generic';

        $this->db->query($sql);
        $this->db->bind(1, $name);
        $this->db->bind(2, $description);
        $this->db->bind(3, $venue_id);
        $this->db->bind(4, $template_type);
        $this->db->bind(5, $canvas_width);
        $this->db->bind(6, $canvas_height);
        $this->db->bind(7, $is_public);
        $this->db->bind(8, $_SESSION['user_id']);
        $this->db->execute();

        $template_id = $this->db->lastInsertId();

        // Save template elements
        if (!empty($elements)) {
            foreach ($elements as $element) {
                $this->saveTemplateElement($template_id, $element);
            }
        }

        // Save template drawings and groups as JSON data
        if (!empty($drawings) || !empty($groups)) {
            $template_data = [
                'drawings' => $drawings,
                'groups' => $groups
            ];

            $sql = "UPDATE layout_templates SET template_data = ? WHERE id = ?";
            $this->db->query($sql);
            $this->db->bind(1, json_encode($template_data));
            $this->db->bind(2, $template_id);
            $this->db->execute();
        }

        return $template_id;
    }

    private function saveTemplateElement($template_id, $element) {
        $sql = "INSERT INTO template_elements
                (template_id, element_type, name, x, y, width, height, rotation, properties)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $this->db->query($sql);
        $this->db->bind(1, $template_id);
        $this->db->bind(2, $element['element_type']);
        $this->db->bind(3, $element['name']);
        $this->db->bind(4, $element['x']);
        $this->db->bind(5, $element['y']);
        $this->db->bind(6, $element['width']);
        $this->db->bind(7, $element['height']);
        $this->db->bind(8, $element['rotation'] ?? 0);
        $this->db->bind(9, json_encode($element['properties'] ?? []));
        $this->db->execute();
    }

    private function createLayoutFromTemplate($show_id, $template_id) {
        // Get template
        $sql = "SELECT * FROM layout_templates WHERE id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $template_id);
        $template = $this->db->single();

        if (!$template) {
            throw new Exception('Template not found');
        }

        // Create layout
        $sql = "INSERT INTO event_layouts
                (show_id, template_id, name, canvas_width, canvas_height, created_by)
                VALUES (?, ?, ?, ?, ?, ?)";

        $this->db->query($sql);
        $this->db->bind(1, $show_id);
        $this->db->bind(2, $template_id);
        $this->db->bind(3, $template->name . ' - ' . date('Y-m-d'));
        $this->db->bind(4, $template->canvas_width);
        $this->db->bind(5, $template->canvas_height);
        $this->db->bind(6, $_SESSION['user_id']);
        $this->db->execute();

        $layout_id = $this->db->lastInsertId();

        // Copy template elements
        $sql = "SELECT * FROM template_elements WHERE template_id = ?";
        $this->db->query($sql);
        $this->db->bind(1, $template_id);
        $templateElements = $this->db->resultSet();
        
        foreach ($templateElements as $element) {
            $sql = "INSERT INTO layout_elements
                    (layout_id, element_type, name, x_position, y_position, width, height,
                     rotation, color, border_color, opacity, z_index, properties)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $this->db->query($sql);
            $this->db->bind(1, $layout_id);
            $this->db->bind(2, $element->element_type);
            $this->db->bind(3, $element->name);
            $this->db->bind(4, $element->x);
            $this->db->bind(5, $element->y);
            $this->db->bind(6, $element->width);
            $this->db->bind(7, $element->height);
            $this->db->bind(8, $element->rotation);
            $this->db->bind(9, $element->color);
            $this->db->bind(10, $element->border_color);
            $this->db->bind(11, $element->opacity);
            $this->db->bind(12, $element->z_index);
            $this->db->bind(13, $element->properties);
            $this->db->execute();
        }

        // Copy template drawings and groups if they exist
        if (!empty($template->template_data)) {
            $template_data = json_decode($template->template_data, true);
            if ($template_data && (isset($template_data['drawings']) || isset($template_data['groups']))) {
                $sql = "UPDATE event_layouts SET layout_data = ? WHERE id = ?";
                $this->db->query($sql);
                $this->db->bind(1, $template->template_data);
                $this->db->bind(2, $layout_id);
                $this->db->execute();
            }
        }

        return $layout_id;
    }
}
