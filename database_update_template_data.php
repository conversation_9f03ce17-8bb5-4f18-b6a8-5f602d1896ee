<?php
/**
 * Database Update Script for Template Data Column
 * Run this script once to add the template_data column to layout_templates table
 * and fix column names in template_elements table
 */

require_once 'config/config.php';
require_once 'libraries/Database.php';

// Initialize database connection
$db = new Database();

echo "<h2>Database Update: Adding template_data column</h2>";
echo "<pre>";

try {
    // Check if template_data column exists
    $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'layout_templates' 
            AND COLUMN_NAME = 'template_data'";
    
    $db->query($sql);
    $result = $db->single();
    
    if ($result->count == 0) {
        echo "Adding template_data column to layout_templates table...\n";
        
        $sql = "ALTER TABLE layout_templates ADD COLUMN template_data LONGTEXT NULL AFTER background_color";
        $db->query($sql);
        $db->execute();
        
        echo "✓ template_data column added successfully\n";
    } else {
        echo "✓ template_data column already exists\n";
    }
    
    // Check if template_elements table has x column (instead of x_position)
    $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'template_elements' 
            AND COLUMN_NAME = 'x'";
    
    $db->query($sql);
    $result = $db->single();
    
    if ($result->count == 0) {
        echo "Updating template_elements column names...\n";
        
        // Change x_position to x
        $sql = "ALTER TABLE template_elements CHANGE COLUMN x_position x INT NOT NULL";
        $db->query($sql);
        $db->execute();
        
        // Change y_position to y
        $sql = "ALTER TABLE template_elements CHANGE COLUMN y_position y INT NOT NULL";
        $db->query($sql);
        $db->execute();
        
        echo "✓ template_elements column names updated successfully\n";
    } else {
        echo "✓ template_elements columns already have correct names\n";
    }
    
    echo "\n<strong>Database update completed successfully!</strong>\n";
    echo "You can now use the Save Template functionality.\n";
    
} catch (Exception $e) {
    echo "❌ Error updating database: " . $e->getMessage() . "\n";
    echo "Please check your database connection and permissions.\n";
}

echo "</pre>";
echo "<p><a href='javascript:history.back()'>← Go Back</a></p>";
?>
