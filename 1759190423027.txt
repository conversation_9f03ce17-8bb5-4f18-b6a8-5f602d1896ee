apple-device-compatibility.js:18 🍎 Apple Device Compatibility: Initializing...
apple-device-compatibility.js:19 🍎 Is Apple Device: false
apple-device-compatibility.js:20 🍎 Is iOS Safari: false
apple-device-compatibility.js:21 🍎 Is PWA Standalone: false
apple-device-compatibility.js:22 🍎 Is iPad: false
apple-device-compatibility.js:23 🍎 Screen dimensions: 1920x919
apple-device-compatibility.js:24 🍎 Device pixel ratio: 1
fcm-notifications.js:16 [FCM] fcm-notifications.js loaded
fcm-notifications.js:497 [FCM] FCMNotificationManager class made globally available
9:9886 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1759190355:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759190355:500 CameraBanner loaded - version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759190355:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759190355:54 CameraBanner: About to load site logo
apple-device-compatibility.js:35 🍎 Not an Apple device, skipping Apple-specific enhancements
header-settings.js:85 📱 Header Settings: Initializing...
header-settings.js:115 📱 Header Settings: Loading from https://events.rowaneliterides.com/home/<USER>
fcm-notifications.js:459 [FCM] DOM loaded, checking initialization conditions...
fcm-notifications.js:463 [FCM] User logged in, initializing FCM manager...
fcm-notifications.js:23 [FCM] FCMNotificationManager constructor called
fcm-notifications.js:47 [FCM] Support check: {serviceWorker: true, pushManager: true, notification: true, isIOS: false, supported: true}
fcm-notifications.js:36 [FCM] FCM Notification Manager initialized
fcm-notifications.js:37 [FCM] Is supported: true
fcm-notifications.js:38 [FCM] Debug mode: false
9:7168 Found 18 roads for intersection detection
9:7504 Checking overlap between road draw_1 and draw_2
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_1 and draw_2
9:7477 Intersection detected between road draw_1 and road draw_2
9:7504 Checking overlap between road draw_1 and draw_3
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7511 No bounding box overlap between roads draw_1 and draw_3
9:7504 Checking overlap between road draw_1 and draw_4
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_1 and draw_4
9:7477 Intersection detected between road draw_1 and road draw_4
9:7504 Checking overlap between road draw_1 and draw_6
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7511 No bounding box overlap between roads draw_1 and draw_6
9:7504 Checking overlap between road draw_1 and draw_7
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_1 and draw_7
9:7477 Intersection detected between road draw_1 and road draw_7
9:7504 Checking overlap between road draw_1 and draw_25
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7511 No bounding box overlap between roads draw_1 and draw_25
9:7504 Checking overlap between road draw_1 and draw_26
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7511 No bounding box overlap between roads draw_1 and draw_26
9:7504 Checking overlap between road draw_1 and draw_29
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_1 and draw_29
9:7504 Checking overlap between road draw_1 and draw_30
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_1 and draw_30
9:7504 Checking overlap between road draw_1 and draw_31
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_1 and draw_31
9:7504 Checking overlap between road draw_1 and draw_32
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_1 and draw_32
9:7504 Checking overlap between road draw_1 and draw_33
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_1 and draw_33
9:7504 Checking overlap between road draw_1 and draw_34
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_1 and draw_34
9:7504 Checking overlap between road draw_1 and draw_35
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_1 and draw_35
9:7504 Checking overlap between road draw_1 and draw_36
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_1 and draw_36
9:7504 Checking overlap between road draw_1 and draw_37
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_1 and draw_37
9:7504 Checking overlap between road draw_1 and draw_38
9:7505 Road draw_1 bounds: {minX: 415.5003099862057, maxX: 437.4996900137943, minY: 157.64001228335596, maxY: 411.79748771664407}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_1 and draw_38
9:7504 Checking overlap between road draw_2 and draw_3
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_2 and draw_3
9:7477 Intersection detected between road draw_2 and road draw_3
9:7504 Checking overlap between road draw_2 and draw_4
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7511 No bounding box overlap between roads draw_2 and draw_4
9:7504 Checking overlap between road draw_2 and draw_6
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7511 No bounding box overlap between roads draw_2 and draw_6
9:7504 Checking overlap between road draw_2 and draw_7
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7511 No bounding box overlap between roads draw_2 and draw_7
9:7504 Checking overlap between road draw_2 and draw_25
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7511 No bounding box overlap between roads draw_2 and draw_25
9:7504 Checking overlap between road draw_2 and draw_26
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7511 No bounding box overlap between roads draw_2 and draw_26
9:7504 Checking overlap between road draw_2 and draw_29
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_2 and draw_29
9:7504 Checking overlap between road draw_2 and draw_30
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_2 and draw_30
9:7504 Checking overlap between road draw_2 and draw_31
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_2 and draw_31
9:7504 Checking overlap between road draw_2 and draw_32
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_2 and draw_32
9:7504 Checking overlap between road draw_2 and draw_33
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_2 and draw_33
9:7504 Checking overlap between road draw_2 and draw_34
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_2 and draw_34
9:7504 Checking overlap between road draw_2 and draw_35
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_2 and draw_35
9:7504 Checking overlap between road draw_2 and draw_36
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_2 and draw_36
9:7504 Checking overlap between road draw_2 and draw_37
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_2 and draw_37
9:7504 Checking overlap between road draw_2 and draw_38
9:7505 Road draw_2 bounds: {minX: 425.4561407727283, maxX: 653.5438592272717, minY: 388.71884618205337, maxY: 409.71865381794663}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_2 and draw_38
9:7504 Checking overlap between road draw_3 and draw_4
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7511 No bounding box overlap between roads draw_3 and draw_4
9:7504 Checking overlap between road draw_3 and draw_6
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7511 No bounding box overlap between roads draw_3 and draw_6
9:7504 Checking overlap between road draw_3 and draw_7
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7511 No bounding box overlap between roads draw_3 and draw_7
9:7504 Checking overlap between road draw_3 and draw_25
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7511 No bounding box overlap between roads draw_3 and draw_25
9:7504 Checking overlap between road draw_3 and draw_26
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7511 No bounding box overlap between roads draw_3 and draw_26
9:7504 Checking overlap between road draw_3 and draw_29
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_3 and draw_29
9:7477 Intersection detected between road draw_3 and road draw_29
9:7504 Checking overlap between road draw_3 and draw_30
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_3 and draw_30
9:7504 Checking overlap between road draw_3 and draw_31
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_3 and draw_31
9:7504 Checking overlap between road draw_3 and draw_32
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_3 and draw_32
9:7504 Checking overlap between road draw_3 and draw_33
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_3 and draw_33
9:7504 Checking overlap between road draw_3 and draw_34
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_3 and draw_34
9:7504 Checking overlap between road draw_3 and draw_35
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_3 and draw_35
9:7504 Checking overlap between road draw_3 and draw_36
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_3 and draw_36
9:7504 Checking overlap between road draw_3 and draw_37
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_3 and draw_37
9:7504 Checking overlap between road draw_3 and draw_38
9:7505 Road draw_3 bounds: {minX: 628.5001133767566, maxX: 649.4998866232434, minY: 406.67113149227026, maxY: 616.7663685077297}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_3 and draw_38
9:7504 Checking overlap between road draw_4 and draw_6
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7511 No bounding box overlap between roads draw_4 and draw_6
9:7504 Checking overlap between road draw_4 and draw_7
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7511 No bounding box overlap between roads draw_4 and draw_7
9:7504 Checking overlap between road draw_4 and draw_25
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7511 No bounding box overlap between roads draw_4 and draw_25
9:7504 Checking overlap between road draw_4 and draw_26
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7511 No bounding box overlap between roads draw_4 and draw_26
9:7504 Checking overlap between road draw_4 and draw_29
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_4 and draw_29
9:7504 Checking overlap between road draw_4 and draw_30
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_4 and draw_30
9:7504 Checking overlap between road draw_4 and draw_31
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_4 and draw_31
9:7504 Checking overlap between road draw_4 and draw_32
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_4 and draw_32
9:7504 Checking overlap between road draw_4 and draw_33
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_4 and draw_33
9:7504 Checking overlap between road draw_4 and draw_34
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_4 and draw_34
9:7504 Checking overlap between road draw_4 and draw_35
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_4 and draw_35
9:7504 Checking overlap between road draw_4 and draw_36
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_4 and draw_36
9:7504 Checking overlap between road draw_4 and draw_37
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_4 and draw_37
9:7504 Checking overlap between road draw_4 and draw_38
9:7505 Road draw_4 bounds: {minX: 184.45771707988442, maxX: 657.5422829201156, minY: 224.71883939266624, maxY: 246.71866060733376}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_4 and draw_38
9:7504 Checking overlap between road draw_6 and draw_7
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7511 No bounding box overlap between roads draw_6 and draw_7
9:7504 Checking overlap between road draw_6 and draw_25
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_6 and draw_25
9:7477 Intersection detected between road draw_6 and road draw_25
9:7504 Checking overlap between road draw_6 and draw_26
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7511 No bounding box overlap between roads draw_6 and draw_26
9:7504 Checking overlap between road draw_6 and draw_29
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_6 and draw_29
9:7504 Checking overlap between road draw_6 and draw_30
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_6 and draw_30
9:7477 Intersection detected between road draw_6 and road draw_30
9:7504 Checking overlap between road draw_6 and draw_31
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_6 and draw_31
9:7504 Checking overlap between road draw_6 and draw_32
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_6 and draw_32
9:7504 Checking overlap between road draw_6 and draw_33
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_6 and draw_33
9:7504 Checking overlap between road draw_6 and draw_34
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_6 and draw_34
9:7504 Checking overlap between road draw_6 and draw_35
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_6 and draw_35
9:7504 Checking overlap between road draw_6 and draw_36
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_6 and draw_36
9:7504 Checking overlap between road draw_6 and draw_37
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_6 and draw_37
9:7504 Checking overlap between road draw_6 and draw_38
9:7505 Road draw_6 bounds: {minX: 34.487452958433394, maxX: 831.5125470415666, minY: 3.7187578714157006, maxY: 24.7187421285843}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_6 and draw_38
9:7504 Checking overlap between road draw_7 and draw_25
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7511 No bounding box overlap between roads draw_7 and draw_25
9:7504 Checking overlap between road draw_7 and draw_26
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7511 No bounding box overlap between roads draw_7 and draw_26
9:7504 Checking overlap between road draw_7 and draw_29
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_7 and draw_29
9:7504 Checking overlap between road draw_7 and draw_30
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_7 and draw_30
9:7504 Checking overlap between road draw_7 and draw_31
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_7 and draw_31
9:7504 Checking overlap between road draw_7 and draw_32
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_7 and draw_32
9:7504 Checking overlap between road draw_7 and draw_33
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_7 and draw_33
9:7504 Checking overlap between road draw_7 and draw_34
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_7 and draw_34
9:7504 Checking overlap between road draw_7 and draw_35
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_7 and draw_35
9:7504 Checking overlap between road draw_7 and draw_36
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_7 and draw_36
9:7504 Checking overlap between road draw_7 and draw_37
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_7 and draw_37
9:7504 Checking overlap between road draw_7 and draw_38
9:7505 Road draw_7 bounds: {minX: 178.46197746116326, maxX: 441.53802253883674, minY: 327.71882228593427, maxY: 348.71867771406573}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_7 and draw_38
9:7504 Checking overlap between road draw_25 and draw_26
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_25 and draw_26
9:7477 Intersection detected between road draw_25 and road draw_26
9:7504 Checking overlap between road draw_25 and draw_29
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_25 and draw_29
9:7504 Checking overlap between road draw_25 and draw_30
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_25 and draw_30
9:7504 Checking overlap between road draw_25 and draw_31
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_25 and draw_31
9:7504 Checking overlap between road draw_25 and draw_32
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_25 and draw_32
9:7504 Checking overlap between road draw_25 and draw_33
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_25 and draw_33
9:7504 Checking overlap between road draw_25 and draw_34
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_25 and draw_34
9:7504 Checking overlap between road draw_25 and draw_35
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_25 and draw_35
9:7504 Checking overlap between road draw_25 and draw_36
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_25 and draw_36
9:7504 Checking overlap between road draw_25 and draw_37
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_25 and draw_37
9:7504 Checking overlap between road draw_25 and draw_38
9:7505 Road draw_25 bounds: {minX: 16.5, maxX: 36.5, minY: 1.71875, maxY: 895.71875}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_25 and draw_38
9:7504 Checking overlap between road draw_26 and draw_29
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7511 No bounding box overlap between roads draw_26 and draw_29
9:7504 Checking overlap between road draw_26 and draw_30
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_26 and draw_30
9:7477 Intersection detected between road draw_26 and road draw_30
9:7504 Checking overlap between road draw_26 and draw_31
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_26 and draw_31
9:7504 Checking overlap between road draw_26 and draw_32
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_26 and draw_32
9:7504 Checking overlap between road draw_26 and draw_33
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_26 and draw_33
9:7504 Checking overlap between road draw_26 and draw_34
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_26 and draw_34
9:7504 Checking overlap between road draw_26 and draw_35
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_26 and draw_35
9:7504 Checking overlap between road draw_26 and draw_36
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_26 and draw_36
9:7504 Checking overlap between road draw_26 and draw_37
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_26 and draw_37
9:7504 Checking overlap between road draw_26 and draw_38
9:7505 Road draw_26 bounds: {minX: 24.487639070011905, maxX: 833.5123609299881, minY: 867.7187576396324, maxY: 888.7187423603676}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_26 and draw_38
9:7504 Checking overlap between road draw_29 and draw_30
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7511 No bounding box overlap between roads draw_29 and draw_30
9:7504 Checking overlap between road draw_29 and draw_31
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_29 and draw_31
9:7504 Checking overlap between road draw_29 and draw_32
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_29 and draw_32
9:7504 Checking overlap between road draw_29 and draw_33
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_29 and draw_33
9:7504 Checking overlap between road draw_29 and draw_34
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_29 and draw_34
9:7504 Checking overlap between road draw_29 and draw_35
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_29 and draw_35
9:7504 Checking overlap between road draw_29 and draw_36
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_29 and draw_36
9:7504 Checking overlap between road draw_29 and draw_37
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_29 and draw_37
9:7504 Checking overlap between road draw_29 and draw_38
9:7505 Road draw_29 bounds: {minX: 625.5, maxX: 776.5, minY: 610.71875, maxY: 630.71875}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_29 and draw_38
9:7504 Checking overlap between road draw_30 and draw_31
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7511 No bounding box overlap between roads draw_30 and draw_31
9:7504 Checking overlap between road draw_30 and draw_32
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7511 No bounding box overlap between roads draw_30 and draw_32
9:7504 Checking overlap between road draw_30 and draw_33
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_30 and draw_33
9:7504 Checking overlap between road draw_30 and draw_34
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_30 and draw_34
9:7504 Checking overlap between road draw_30 and draw_35
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_30 and draw_35
9:7504 Checking overlap between road draw_30 and draw_36
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_30 and draw_36
9:7504 Checking overlap between road draw_30 and draw_37
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_30 and draw_37
9:7504 Checking overlap between road draw_30 and draw_38
9:7505 Road draw_30 bounds: {minX: 829.5, maxX: 849.5, minY: 4.71875, maxY: 889.71875}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_30 and draw_38
9:7504 Checking overlap between road draw_31 and draw_32
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_31 and draw_32
9:7477 Intersection detected between road draw_31 and road draw_32
9:7504 Checking overlap between road draw_31 and draw_33
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_31 and draw_33
9:7477 Intersection detected between road draw_31 and road draw_33
9:7504 Checking overlap between road draw_31 and draw_34
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7511 No bounding box overlap between roads draw_31 and draw_34
9:7504 Checking overlap between road draw_31 and draw_35
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_31 and draw_35
9:7504 Checking overlap between road draw_31 and draw_36
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_31 and draw_36
9:7504 Checking overlap between road draw_31 and draw_37
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_31 and draw_37
9:7504 Checking overlap between road draw_31 and draw_38
9:7505 Road draw_31 bounds: {minX: 142.5, maxX: 671.5, minY: 1087.71875, maxY: 1107.71875}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_31 and draw_38
9:7504 Checking overlap between road draw_32 and draw_33
9:7505 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7506 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7511 No bounding box overlap between roads draw_32 and draw_33
9:7504 Checking overlap between road draw_32 and draw_34
9:7505 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_32 and draw_34
9:7477 Intersection detected between road draw_32 and road draw_34
9:7504 Checking overlap between road draw_32 and draw_35
9:7505 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_32 and draw_35
9:7504 Checking overlap between road draw_32 and draw_36
9:7505 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_32 and draw_36
9:7504 Checking overlap between road draw_32 and draw_37
9:7505 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_32 and draw_37
9:7504 Checking overlap between road draw_32 and draw_38
9:7505 Road draw_32 bounds: {minX: 214.50024622051512, maxX: 236.49975377948488, minY: 1069.6485762892669, maxY: 1354.7889237107331}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_32 and draw_38
9:7504 Checking overlap between road draw_33 and draw_34
9:7505 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7506 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_33 and draw_34
9:7477 Intersection detected between road draw_33 and road draw_34
9:7504 Checking overlap between road draw_33 and draw_35
9:7505 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7511 No bounding box overlap between roads draw_33 and draw_35
9:7504 Checking overlap between road draw_33 and draw_36
9:7505 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_33 and draw_36
9:7477 Intersection detected between road draw_33 and road draw_36
9:7504 Checking overlap between road draw_33 and draw_37
9:7505 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_33 and draw_37
9:7504 Checking overlap between road draw_33 and draw_38
9:7505 Road draw_33 bounds: {minX: 400.5002207408062, maxX: 422.4997792591938, minY: 1001.6523062839921, maxY: 1302.7851937160078}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_33 and draw_38
9:7477 Intersection detected between road draw_33 and road draw_38
9:7504 Checking overlap between road draw_34 and draw_35
9:7505 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7506 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_34 and draw_35
9:7477 Intersection detected between road draw_34 and road draw_35
9:7504 Checking overlap between road draw_34 and draw_36
9:7505 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_34 and draw_36
9:7504 Checking overlap between road draw_34 and draw_37
9:7505 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7515 Bounding boxes overlap, checking detailed overlap...
9:7519 Detailed overlap confirmed between roads draw_34 and draw_37
9:7477 Intersection detected between road draw_34 and road draw_37
9:7504 Checking overlap between road draw_34 and draw_38
9:7505 Road draw_34 bounds: {minX: 175.44329013687178, maxX: 704.5567098631283, minY: 1204.7189108017217, maxY: 1227.7185891982783}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_34 and draw_38
9:7504 Checking overlap between road draw_35 and draw_36
9:7505 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7506 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7511 No bounding box overlap between roads draw_35 and draw_36
9:7504 Checking overlap between road draw_35 and draw_37
9:7505 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_35 and draw_37
9:7504 Checking overlap between road draw_35 and draw_38
9:7505 Road draw_35 bounds: {minX: 649.5123005908163, maxX: 676.4876994091837, minY: 1220.22290676692, maxY: 1362.21459323308}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_35 and draw_38
9:7504 Checking overlap between road draw_36 and draw_37
9:7505 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7506 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7511 No bounding box overlap between roads draw_36 and draw_37
9:7504 Checking overlap between road draw_36 and draw_38
9:7505 Road draw_36 bounds: {minX: 410.38372879037854, maxX: 496.61627120962146, minY: 1267.7194259725563, maxY: 1288.7180740274437}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_36 and draw_38
9:7504 Checking overlap between road draw_37 and draw_38
9:7505 Road draw_37 bounds: {minX: 267.5019994001999, maxX: 289.4980005998001, minY: 1215.518789988004, maxY: 1315.918710011996}
9:7506 Road draw_38 bounds: {minX: 344.427009243694, maxX: 618.572990756306, minY: 992.7190163860733, maxY: 1014.7184836139267}
9:7511 No bounding box overlap between roads draw_37 and draw_38
9:7170 Found 17 intersections
9:7280 Road draw_1 has 3 intersections
9:7783 Road draw_1 top edge hidden: 1/4 points inside other roads
9:7783 Road draw_1 bottom edge hidden: 1/4 points inside other roads
9:7280 Road draw_2 has 2 intersections
9:7783 Road draw_2 top edge hidden: 1/4 points inside other roads
9:7783 Road draw_2 bottom edge hidden: 1/4 points inside other roads
9:7783 Road draw_2 left edge hidden: 4/4 points inside other roads
9:7280 Road draw_3 has 2 intersections
9:7783 Road draw_3 top edge hidden: 2/4 points inside other roads
9:7783 Road draw_3 bottom edge hidden: 2/4 points inside other roads
9:7783 Road draw_3 left edge hidden: 4/4 points inside other roads
9:7783 Road draw_3 right edge hidden: 4/4 points inside other roads
9:7280 Road draw_4 has 1 intersections
9:7280 Road draw_6 has 2 intersections
9:7783 Road draw_6 top edge hidden: 2/4 points inside other roads
9:7783 Road draw_6 bottom edge hidden: 2/4 points inside other roads
9:7783 Road draw_6 left edge hidden: 4/4 points inside other roads
9:7783 Road draw_6 right edge hidden: 4/4 points inside other roads
9:7280 Road draw_7 has 1 intersections
9:7280 Road draw_25 has 2 intersections
9:7280 Road draw_26 has 2 intersections
9:7783 Road draw_26 top edge hidden: 2/4 points inside other roads
9:7783 Road draw_26 bottom edge hidden: 2/4 points inside other roads
9:7783 Road draw_26 left edge hidden: 4/4 points inside other roads
9:7783 Road draw_26 right edge hidden: 4/4 points inside other roads
9:7280 Road draw_29 has 1 intersections
9:7280 Road draw_30 has 2 intersections
9:7783 Road draw_30 top edge hidden: 1/4 points inside other roads
9:7783 Road draw_30 left edge hidden: 1/4 points inside other roads
9:7280 Road draw_31 has 2 intersections
9:7280 Road draw_32 has 2 intersections
9:7280 Road draw_33 has 4 intersections
9:7783 Road draw_33 top edge hidden: 2/4 points inside other roads
9:7783 Road draw_33 bottom edge hidden: 2/4 points inside other roads
9:7783 Road draw_33 left edge hidden: 4/4 points inside other roads
9:7280 Road draw_34 has 4 intersections
9:7280 Road draw_35 has 1 intersections
9:7783 Road draw_35 top edge hidden: 1/4 points inside other roads
9:7783 Road draw_35 bottom edge hidden: 1/4 points inside other roads
9:7783 Road draw_35 left edge hidden: 4/4 points inside other roads
9:7280 Road draw_36 has 1 intersections
9:7783 Road draw_36 top edge hidden: 1/4 points inside other roads
9:7783 Road draw_36 bottom edge hidden: 1/4 points inside other roads
9:7783 Road draw_36 left edge hidden: 4/4 points inside other roads
9:7280 Road draw_37 has 1 intersections
9:7783 Road draw_37 top edge hidden: 1/4 points inside other roads
9:7783 Road draw_37 bottom edge hidden: 1/4 points inside other roads
9:7783 Road draw_37 left edge hidden: 4/4 points inside other roads
9:7280 Road draw_38 has 1 intersections
main.js:10 [Main] DOM loaded, initializing...
main.js:16 [Main] CSRF token found
main.js:20 [Main] Main.js initialization complete
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js?v=1759190355:1158 [NotificationManager] DOM ready, initializing...
notifications.js?v=1759190355:1161 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1759190355:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1759190355:33 [PWA] Initializing PWA features...
camera-banner.js?v=1759190355:508 CameraBanner: Starting initialization...
9:9955 [PWA] PWA features initialized successfully
header-settings.js:118 📱 Header Settings: Response status 200
pwa-features.js?v=1759190355:115 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1759190355:116 [PWA] Caching disabled - all content will be fetched fresh
camera-banner.js?v=1759190355:57 CameraBanner: About to load banners
header-settings.js:122 📱 Header Settings: Raw response {success: true, settings: {…}, timestamp: 1759190355}
header-settings.js:126 📱 Header Settings: Loaded from controller {sticky_header_desktop: '0', sticky_header_ipad_portrait: '0', sticky_header_ipad_landscape: '0', sticky_header_android_portrait: '0', sticky_header_android_landscape: '0', …}
header-settings.js:162 📱 Header Settings: Device Detection {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:163 📱 Header Settings: Applying {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:281 📱 Header Settings: Sticky header disabled (forced)
header-settings.js:307 📱 Header Settings: Orientation handling setup
notification-center.js?v=1759190355:148 [NotificationCenter] Desktop bell reset to normal
pwa-features.js?v=1759190355:204 [PWA] IndexedDB initialized
pwa-features.js?v=1759190355:364 [PWA] Push notifications handled by FCM system (fcm-notifications.js)
pwa-features.js?v=1759190355:59 [PWA] About to check for forced update...
pwa-features.js?v=1759190355:2835 [PWA] Checking forced update flags: {forceUpdate: null, cacheCleared: null, showNotification: null, forceUpdateAfterClear: null, cookieForceUpdate: 'not found'}
pwa-features.js?v=1759190355:64 [PWA] PWA features initialized
pwa-features.js?v=1759190355:70 [PWA] Setting up update banner check in 3 seconds...
camera-banner.js?v=1759190355:60 CameraBanner: Initialization complete, banners loaded: 5
camera-banner.js?v=1759190355:86 Fetch finished loading: GET "https://events.rowaneliterides.com/api/getSiteLogo".
loadSiteLogo @ camera-banner.js?v=1759190355:86
init @ camera-banner.js?v=1759190355:55
(anonymous) @ camera-banner.js?v=1759190355:36
setTimeout
CameraBanner @ camera-banner.js?v=1759190355:35
(anonymous) @ camera-banner.js?v=1759190355:499
header-settings.js:117 Fetch finished loading: GET "https://events.rowaneliterides.com/home/<USER>".
loadHeaderSettings @ header-settings.js:117
initHeaderSettings @ header-settings.js:88
notification-center.js?v=1759190355:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759190355:69
init @ notification-center.js?v=1759190355:42
NotificationCenter @ notification-center.js?v=1759190355:15
(anonymous) @ notification-center.js?v=1759190355:362
notifications.js?v=1759190355:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759190355:554
init @ notifications.js?v=1759190355:69
NotificationManager @ notifications.js?v=1759190355:46
(anonymous) @ notifications.js?v=1759190355:1160
camera-banner.js?v=1759190355:119 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759190355:119
initializeBannerSystem @ camera-banner.js?v=1759190355:509
camera-banner.js?v=1759190355:119 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759190355:119
init @ camera-banner.js?v=1759190355:58
await in init
(anonymous) @ camera-banner.js?v=1759190355:36
setTimeout
CameraBanner @ camera-banner.js?v=1759190355:35
(anonymous) @ camera-banner.js?v=1759190355:499
camera-banner.js?v=1759190355:137 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759190355:137
await in loadBanners
initializeBannerSystem @ camera-banner.js?v=1759190355:509
camera-banner.js?v=1759190355:137 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759190355:137
await in loadBanners
init @ camera-banner.js?v=1759190355:58
await in init
(anonymous) @ camera-banner.js?v=1759190355:36
setTimeout
CameraBanner @ camera-banner.js?v=1759190355:35
(anonymous) @ camera-banner.js?v=1759190355:499
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
fcm-notifications.js:468 [FCM] Starting delayed initialization...
fcm-notifications.js:63 [FCM] Initializing Firebase...
fcm-notifications.js:71 [FCM] Firebase SDK version: 9.23.0
fcm-notifications.js:114 [FCM] Ensuring unified service worker is registered...
fcm-notifications.js:127 [FCM] Unified service worker already registered: https://events.rowaneliterides.com/
fcm-notifications.js:148 [FCM] Unified service worker is active and ready
fcm-notifications.js:78 [FCM] Initializing Firebase app...
fcm-notifications.js:94 [FCM] Using unified service worker for Firebase messaging: https://events.rowaneliterides.com/
fcm-notifications.js:99 [FCM] Firebase messaging initialized with unified service worker
fcm-notifications.js:158 [FCM] Getting VAPID key...
pwa-features.js?v=1759190355:829 [PWA] Found 0 FAB camera buttons
pwa-features.js?v=1759190355:72 [PWA] 3 seconds elapsed, checking for update banner flag now...
pwa-features.js?v=1759190355:2918 [PWA] Checking for update banner flag...
pwa-features.js?v=1759190355:2921 [PWA] Update banner flag: null
pwa-features.js?v=1759190355:2932 [PWA] No update banner flag found
notifications.js?v=1759190355:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759190355:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ desktop-notifications-fix.js:173
setTimeout
enhanceNotificationManager @ desktop-notifications-fix.js:171
checkForManager @ desktop-notifications-fix.js:114
waitForNotificationManager @ desktop-notifications-fix.js:122
initDesktopFix @ desktop-notifications-fix.js:38
(anonymous) @ desktop-notifications-fix.js:209
fcm-notifications.js:166 [FCM] VAPID key obtained
fcm-notifications.js:104 [FCM] Firebase initialized successfully
fcm-notifications.js:253 [FCM] Setting up foreground message handling...
fcm-notifications.js:478 [FCM] Permission already granted, getting token...
fcm-notifications.js:178 [FCM] requestPermissionAndGetToken called
fcm-notifications.js:283 [FCM] Checking cached token: exists
fcm-notifications.js:161 Fetch finished loading: GET "https://events.rowaneliterides.com/api/pwa/vapid-key".
getVAPIDKey @ fcm-notifications.js:161
initializeFirebase @ fcm-notifications.js:102
await in initializeFirebase
(anonymous) @ fcm-notifications.js:473
setTimeout
(anonymous) @ fcm-notifications.js:467
fcm-notifications.js:306 [FCM] Cached token is valid
fcm-notifications.js:189 [FCM] Using cached FCM token
fcm-notifications.js:329 Fetch finished loading: POST "https://events.rowaneliterides.com/api/pwa/fcm-verify-token".
verifyTokenInDatabase @ fcm-notifications.js:329
getCachedToken @ fcm-notifications.js:299
requestPermissionAndGetToken @ fcm-notifications.js:187
(anonymous) @ fcm-notifications.js:479
setTimeout
(anonymous) @ fcm-notifications.js:467
notification-center.js?v=1759190355:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759190355:69
(anonymous) @ notification-center.js?v=1759190355:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759190355:177
init @ notification-center.js?v=1759190355:45
NotificationCenter @ notification-center.js?v=1759190355:15
(anonymous) @ notification-center.js?v=1759190355:362
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759190355:148 [NotificationCenter] Desktop bell reset to normal
notifications.js?v=1759190355:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759190355:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759190355:77
setInterval
init @ notifications.js?v=1759190355:76
NotificationManager @ notifications.js?v=1759190355:46
(anonymous) @ notifications.js?v=1759190355:1160
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759190355:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759190355:69
(anonymous) @ notification-center.js?v=1759190355:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759190355:177
init @ notification-center.js?v=1759190355:45
NotificationCenter @ notification-center.js?v=1759190355:15
(anonymous) @ notification-center.js?v=1759190355:362
notification-center.js?v=1759190355:148 [NotificationCenter] Desktop bell reset to normal
notifications.js?v=1759190355:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759190355:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759190355:77
setInterval
init @ notifications.js?v=1759190355:76
NotificationManager @ notifications.js?v=1759190355:46
(anonymous) @ notifications.js?v=1759190355:1160
