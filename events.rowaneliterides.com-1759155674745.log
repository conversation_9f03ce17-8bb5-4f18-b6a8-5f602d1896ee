apple-device-compatibility.js:18 🍎 Apple Device Compatibility: Initializing...
apple-device-compatibility.js:19 🍎 Is Apple Device: false
apple-device-compatibility.js:20 🍎 Is iOS Safari: false
apple-device-compatibility.js:21 🍎 Is PWA Standalone: false
apple-device-compatibility.js:22 🍎 Is iPad: false
apple-device-compatibility.js:23 🍎 Screen dimensions: 1920x919
apple-device-compatibility.js:24 🍎 Device pixel ratio: 1
fcm-notifications.js:16 [FCM] fcm-notifications.js loaded
fcm-notifications.js:497 [FCM] FCMNotificationManager class made globally available
9:2718 🔄 Loaded 1 groups from database
9:8899 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1759155476:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759155476:500 CameraBanner loaded - version: 3.64.0-cache-bust-fix
apple-device-compatibility.js:35 🍎 Not an Apple device, skipping Apple-specific enhancements
header-settings.js:85 📱 Header Settings: Initializing...
header-settings.js:115 📱 Header Settings: Loading from https://events.rowaneliterides.com/home/<USER>
fcm-notifications.js:459 [FCM] DOM loaded, checking initialization conditions...
fcm-notifications.js:463 [FCM] User logged in, initializing FCM manager...
fcm-notifications.js:23 [FCM] FCMNotificationManager constructor called
fcm-notifications.js:47 [FCM] Support check: {serviceWorker: true, pushManager: true, notification: true, isIOS: false, supported: true}
fcm-notifications.js:36 [FCM] FCM Notification Manager initialized
fcm-notifications.js:37 [FCM] Is supported: true
fcm-notifications.js:38 [FCM] Debug mode: false
9:6273 Drawing canvas initialized
9:6441 Canvas size: 2000 x 1400
9:6442 Drawing events attached to design-canvas
9:2738 🔗 Reconnected canvas drawing: rectangle with ID: draw_1
9:2738 🔗 Reconnected canvas drawing: rectangle with ID: draw_2
9:2743 ✅ Group element references reconnected for both grid and canvas elements
9:2682 🔢 Initialized drawingCounter to: 3 based on existing drawings
9:8465 Saved to history: Initial state History length: 1
main.js:10 [Main] DOM loaded, initializing...
main.js:16 [Main] CSRF token found
main.js:20 [Main] Main.js initialization complete
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js?v=1759155476:1158 [NotificationManager] DOM ready, initializing...
notifications.js?v=1759155476:1161 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1759155476:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1759155476:33 [PWA] Initializing PWA features...
camera-banner.js?v=1759155476:508 CameraBanner: Starting initialization...
9:8968 [PWA] PWA features initialized successfully
camera-banner.js?v=1759155476:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1759155476:54 CameraBanner: About to load site logo
pwa-features.js?v=1759155476:115 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1759155476:116 [PWA] Caching disabled - all content will be fetched fresh
pwa-features.js?v=1759155476:204 [PWA] IndexedDB initialized
pwa-features.js?v=1759155476:364 [PWA] Push notifications handled by FCM system (fcm-notifications.js)
pwa-features.js?v=1759155476:59 [PWA] About to check for forced update...
pwa-features.js?v=1759155476:2835 [PWA] Checking forced update flags: {forceUpdate: null, cacheCleared: null, showNotification: null, forceUpdateAfterClear: null, cookieForceUpdate: 'not found'}
pwa-features.js?v=1759155476:64 [PWA] PWA features initialized
pwa-features.js?v=1759155476:70 [PWA] Setting up update banner check in 3 seconds...
header-settings.js:118 📱 Header Settings: Response status 200
header-settings.js:122 📱 Header Settings: Raw response {success: true, settings: {…}, timestamp: 1759155476}
header-settings.js:126 📱 Header Settings: Loaded from controller {sticky_header_desktop: '0', sticky_header_ipad_portrait: '0', sticky_header_ipad_landscape: '0', sticky_header_android_portrait: '0', sticky_header_android_landscape: '0', …}
header-settings.js:162 📱 Header Settings: Device Detection {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:163 📱 Header Settings: Applying {deviceType: 'desktop', isPortrait: false, isPWA: false, screenSize: '1920x919', settingKey: 'sticky_header_desktop', …}
header-settings.js:281 📱 Header Settings: Sticky header disabled (forced)
header-settings.js:307 📱 Header Settings: Orientation handling setup
notification-center.js?v=1759155476:148 [NotificationCenter] Desktop bell reset to normal
camera-banner.js?v=1759155476:57 CameraBanner: About to load banners
 Fetch finished loading: GET "https://events.rowaneliterides.com/home/<USER>".
loadHeaderSettings @ header-settings.js:117
initHeaderSettings @ header-settings.js:88
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
init @ notification-center.js?v=1759155476:42
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759155476:119
initializeBannerSystem @ camera-banner.js?v=1759155476:509
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
init @ notifications.js?v=1759155476:69
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
 Fetch finished loading: GET "https://events.rowaneliterides.com/api/getSiteLogo".
loadSiteLogo @ camera-banner.js?v=1759155476:86
init @ camera-banner.js?v=1759155476:55
(anonymous) @ camera-banner.js?v=1759155476:36
setTimeout
CameraBanner @ camera-banner.js?v=1759155476:35
(anonymous) @ camera-banner.js?v=1759155476:499
 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759155476:137
await in loadBanners
initializeBannerSystem @ camera-banner.js?v=1759155476:509
 Fetch finished loading: GET "https://events.rowaneliterides.com/eventLayoutDesigner/api/banners?zone=camera_qr_top&show_id=&limit=10".
loadBanners @ camera-banner.js?v=1759155476:119
init @ camera-banner.js?v=1759155476:58
await in init
(anonymous) @ camera-banner.js?v=1759155476:36
setTimeout
CameraBanner @ camera-banner.js?v=1759155476:35
(anonymous) @ camera-banner.js?v=1759155476:499
camera-banner.js?v=1759155476:60 CameraBanner: Initialization complete, banners loaded: 5
 Fetch finished loading: GET "https://events.rowaneliterides.com/api/cameraBanners".
loadBanners @ camera-banner.js?v=1759155476:137
await in loadBanners
init @ camera-banner.js?v=1759155476:58
await in init
(anonymous) @ camera-banner.js?v=1759155476:36
setTimeout
CameraBanner @ camera-banner.js?v=1759155476:35
(anonymous) @ camera-banner.js?v=1759155476:499
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ desktop-notifications-fix.js:173
setTimeout
enhanceNotificationManager @ desktop-notifications-fix.js:171
checkForManager @ desktop-notifications-fix.js:114
waitForNotificationManager @ desktop-notifications-fix.js:122
initDesktopFix @ desktop-notifications-fix.js:38
(anonymous) @ desktop-notifications-fix.js:209
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
fcm-notifications.js:468 [FCM] Starting delayed initialization...
fcm-notifications.js:63 [FCM] Initializing Firebase...
fcm-notifications.js:71 [FCM] Firebase SDK version: 9.23.0
fcm-notifications.js:114 [FCM] Ensuring unified service worker is registered...
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
fcm-notifications.js:127 [FCM] Unified service worker already registered: https://events.rowaneliterides.com/
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
fcm-notifications.js:148 [FCM] Unified service worker is active and ready
fcm-notifications.js:78 [FCM] Initializing Firebase app...
fcm-notifications.js:94 [FCM] Using unified service worker for Firebase messaging: https://events.rowaneliterides.com/
fcm-notifications.js:99 [FCM] Firebase messaging initialized with unified service worker
fcm-notifications.js:158 [FCM] Getting VAPID key...
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
fcm-notifications.js:166 [FCM] VAPID key obtained
fcm-notifications.js:104 [FCM] Firebase initialized successfully
fcm-notifications.js:253 [FCM] Setting up foreground message handling...
fcm-notifications.js:478 [FCM] Permission already granted, getting token...
fcm-notifications.js:178 [FCM] requestPermissionAndGetToken called
fcm-notifications.js:283 [FCM] Checking cached token: exists
 Fetch finished loading: GET "https://events.rowaneliterides.com/api/pwa/vapid-key".
getVAPIDKey @ fcm-notifications.js:161
initializeFirebase @ fcm-notifications.js:102
await in initializeFirebase
(anonymous) @ fcm-notifications.js:473
setTimeout
(anonymous) @ fcm-notifications.js:467
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
fcm-notifications.js:306 [FCM] Cached token is valid
fcm-notifications.js:189 [FCM] Using cached FCM token
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
fcm-notifications.js:329 Fetch finished loading: POST "https://events.rowaneliterides.com/api/pwa/fcm-verify-token".
verifyTokenInDatabase @ fcm-notifications.js:329
getCachedToken @ fcm-notifications.js:299
requestPermissionAndGetToken @ fcm-notifications.js:187
(anonymous) @ fcm-notifications.js:479
setTimeout
(anonymous) @ fcm-notifications.js:467
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
pwa-features.js?v=1759155476:829 [PWA] Found 0 FAB camera buttons
pwa-features.js?v=1759155476:72 [PWA] 3 seconds elapsed, checking for update banner flag now...
pwa-features.js?v=1759155476:2918 [PWA] Checking for update banner flag...
pwa-features.js?v=1759155476:2921 [PWA] Update banner flag: null
pwa-features.js?v=1759155476:2932 [PWA] No update banner flag found
9:6191 Selecting drawing tool: text
9:6217 Drawing mode activated for: text
9:6230 Drawing system activated for tool: text
9:6281 Design canvas mousedown - drawing mode active
9:6446 Start drawing called true text
9:6461 Drawing start point: {x: 556.5, y: 832.3125}
9:6293 Design canvas mouseup - stop drawing
9:6510 Stop drawing called
9:8465 Saved to history: Add text: sdfsdfsdfs History length: 2
9:6291 [Violation] 'mouseup' handler took 2137ms
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
notification-center.js?v=1759155476:148 [NotificationCenter] Desktop bell reset to normal
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:208
9:6191 Selecting drawing tool: text
9:6204 Drawing mode deactivated
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): true
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.5, y: 822.3125, width: 110.0390625, height: 40, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 601.51953125 822.3125 rotation: 0 positioned at: 594.51953125 795.3125
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4450 showGeneralDrawingEditor called for: text draw_4
9:4463 Creating editor for: Text
9:4522 General drawing editor created and added to DOM
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:5015 Drawing rotation handle clicked
9:4817 getDrawingBounds called for: text points: [{…}]
9:5527 Starting drawing rotation from angle: -1.5712613565232803
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: -0.000649859434177456 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.4998865810278, y: 822.3119893807101, width: 110.03928933794441, height: 40.001021238579824, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 601.5193044004724 822.3119893819966 rotation: -0.000649859434177456 positioned at: 594.5190775567363 795.311989383283
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 1.3965470782778013 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.2696532725702, y: 821.2182574760128, width: 110.49975595485967, height: 42.18848504797438, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 602.0336386784627 821.2245233020617 rotation: 1.3965470782778013 positioned at: 595.5210773033654 794.2304640947133
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 2.7921166681278375 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.0663215406094, y: 820.131364638217, width: 110.9064194187813, height: 44.3622707235661, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 602.6000263349663 820.15769699232 rotation: 2.7921166681278375 positioned at: 596.5742732049691 793.1814400096846
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 4.184410677556009 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.890337132514, y: 819.0542233884091, width: 111.25838823497202, height: 46.516553223181745, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 603.2166142006529 819.1162214105392 rotation: 4.184410677556009 positioned at: 597.6759510027675 792.169534064065
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 5.571804039517558 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.7417724816404, y: 817.9886600454703, width: 111.55551753671921, height: 48.647679909059434, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 603.8812088654794 818.103582862422 rotation: 5.571804039517558 positioned at: 598.823071350921 791.1980768380167
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 6.952706041257228 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.6205550373099, y: 816.9364207833323, width: 111.79795242538012, height: 50.75215843333535, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 604.591306130304 817.1230260972897 rotation: 6.952706041257228 positioned at: 600.0123065173823 790.2700979221875
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 8.325570916995039 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.5264708304019, y: 815.899157878084, width: 111.98612083919625, height: 52.826684243832005, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 605.3441253096554 816.1775210956995 rotation: 8.325570916995039 positioned at: 601.240081483624 789.3882957991283
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 9.688907674637083 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.4591699875355, y: 814.8784176251961, width: 112.12072252492908, height: 54.868164749607786, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 606.136647440531 815.2697354625001 rotation: 9.688907674637083 positioned at: 602.5026183722006 788.5550140673813
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 11.041289010578252 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.4181740128964, y: 813.8756301127798, width: 112.20271447420714, height: 56.87373977444031, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 606.9656563486178 814.4020128958147 rotation: 11.041289010578252 positioned at: 603.7959830478762 787.7722243748716
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 15.019540469168742 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.4464951422922, y: 810.9873830337611, width: 112.14607221541564, height: 62.650233932477704, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 609.6373869058552 812.0575273095604 rotation: 15.019540469168742 positioned at: 607.820356000339 785.740777288772
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 15.019540469168742}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 15.019540469168742, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 16.3153529541406 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.5036953855705, y: 810.0681377487792, width: 112.03167172885901, height: 64.48872450244153, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 610.5777428559284 811.3666165986464 rotation: 16.3153529541406 positioned at: 609.1962206135931 785.1720155950666
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 16.3153529541406}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 16.3153529541406, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 17.594264651073594 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.5832270413061, y: 809.1720355598054, width: 111.87260841738771, height: 66.28092888038918, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 611.5370477121166 810.7223156503792 rotation: 17.594264651073594 positioned at: 610.5825371953399 784.6578970411418
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 17.594264651073594}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 17.594264651073594, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 18.855355446315425 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.6840607602225, y: 808.2997071290707, width: 111.6709409795551, height: 68.02558574185855, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 612.5117902579118 810.1249197743334 rotation: 18.855355446315425 positioned at: 611.9753929674129 784.1981704492715
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 18.855355446315425}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 18.855355446315425, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 20.097799652684614 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.8051177993785, y: 807.4516507598719, width: 111.42882690124293, height: 69.72169848025624, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 613.4985428110858 809.5744167976757 rotation: 20.097799652684614 positioned at: 613.3710154112639 783.7922678162678
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 20.097799652684614}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 20.097799652684614, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 21.320865867484443 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 545.9452821665993, y: 806.6282369903697, width: 111.14849816680135, height: 71.36852601926057, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 614.4939905619925 809.0705080003933 rotation: 21.320865867484443 positioned at: 614.7658007201516 783.4393304470636
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 21.320865867484443}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 21.320865867484443, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 22.523915875287592 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.1034122734134, y: 805.8297143917952, width: 110.83223795317326, height: 72.96557121640967, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 615.4949567737674 808.6126316447101 rotation: 22.523915875287592 positioned at: 616.1563374993797 783.1382373277854
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 22.523915875287592}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 22.523915875287592, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 23.706402695549667 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.278351933028, y: 805.0562163545763, width: 110.48235863394393, height: 74.5125672908473, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 616.4984237302484 808.1999883490907 rotation: 23.706402695549667 positioned at: 617.5394256846065 782.8876349347966
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 23.706402695549667}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 23.706402695549667, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 24.867867882492654 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.4689405738654, y: 804.3077686465981, width: 110.10118135226912, height: 76.00946270680379, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 617.5015494467816 807.8315675937847 rotation: 24.867867882492654 positioned at: 618.912090780789 782.685967728557
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 24.867867882492654}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 24.867867882492654, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 26.007938188277684 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.6740225728353, y: 803.584297534992, width: 109.69101735432946, height: 77.4564049300161, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 618.5016802707626 807.5061747011802 rotation: 26.007938188277684 positioned at: 620.2715936361486 782.531508652443
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 26.569309882954215 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 546.7815342089621, y: 803.2322211787355, width: 109.47599408207589, height: 78.16055764252906, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 618.9993613172782 807.3593351423386 rotation: 26.569309882954215 positioned at: 620.9449628301144 782.471456185527
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 27.70418107493732 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 547.011985919948, y: 802.529023304458, width: 109.01509066010408, height: 79.5669533910841, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 620.0151343160355 807.0898129189667 rotation: 27.70418107493732 positioned at: 622.3132674116321 782.3826188742692
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 28.815954511192082 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 547.2546794095271, y: 801.851465826734, width: 108.52970368094589, height: 80.92206834653189, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 621.0216554750054 806.8616546351479 rotation: 28.815954511192082 positioned at: 623.6616088886981 782.338204684244
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 29.904516594864866 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 547.5084544056946, y: 801.1991646853827, width: 108.02215368861084, height: 82.2266706292346, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 622.0168343428393 806.6730989582816 rotation: 29.904516594864866 positioned at: 624.9879558468404 782.3359499437391
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 31.61332959359067 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 547.9388325484186, y: 800.1979300556571, width: 107.16139740316271, height: 84.22913988868572, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 623.5953167527506 806.4475213155856 rotation: 31.61332959359067 positioned at: 627.0789975938111 782.415421150815
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 32.28123208028015 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 548.1176073986622, y: 799.8143055965747, width: 106.80384770267551, height: 84.99638880685052, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 624.2167733409797 806.3829616086598 rotation: 32.28123208028015 positioned at: 627.8982822425204 782.4742251808156
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 34.05181164105201 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 548.6200049859107, y: 798.8188417831882, width: 105.79905252817866, height: 86.98731643362362, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 625.8734728734998 806.2766313067106 rotation: 34.05181164105201 positioned at: 630.072320071911 782.7060000143717
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 34.78349885768268 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 548.8396101260133, y: 798.4167550540858, width: 105.35984224797335, height: 87.7914898918284, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 626.5610464945831 806.2603303203252 rotation: 34.78349885768268 positioned at: 630.9705875676133 782.8340595170407
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 35.843862879615095 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 549.1702052913247, y: 797.843876596378, width: 104.69865191735062, height: 88.93724680724404, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 627.559397249863 806.2655327749707 rotation: 35.843862879615095 positioned at: 632.2709655453376 783.0532174541895
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 36.61311016305034 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 549.4191220312437, y: 797.4356560685243, width: 104.20081843751268, height: 89.75368786295144, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 628.2844649570446 806.2907087493205 rotation: 36.61311016305034 positioned at: 633.2126360859739 783.237088175787
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 37.654214927772784 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 549.7680845182864, y: 796.8931929348247, width: 103.5028934634272, height: 90.8386141303506, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 629.2659396760199 806.3534917581536 rotation: 37.654214927772784 positioned at: 634.4838312375115 783.5192527239709
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 38.666951399839526 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 550.1207660009022, y: 796.3767300436828, width: 102.79753049819556, height: 91.87153991263449, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 630.2198509641603 806.4462678340102 rotation: 38.666951399839526 positioned at: 635.7156988584932 783.8304493987921
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 39.47948619962866 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 550.4130846869353, y: 795.9704732346047, width: 102.2128931261293, height: 92.6840535307906, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 630.9838804814457 806.543301391532 rotation: 39.47948619962866 positioned at: 636.6999187249207 784.1062559685737
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 40.469813760051295 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 550.7805385435487, y: 795.4852128315911, width: 101.47798541290263, height: 93.65457433681786, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 631.9126573737495 806.688733944479 rotation: 40.469813760051295 positioned at: 637.8936041514025 784.473773518775
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 41.31611848236322 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 551.1042024428514, y: 795.0792364419666, width: 100.83065761429725, height: 94.4665271160668, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 632.703545312342 806.8366144817737 rotation: 41.31611848236322 positioned at: 638.9078050705218 784.8150458473563
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 42.281761200043334 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 551.4842724953767, y: 794.6259403019037, width: 100.07051750924666, height: 95.37311939619258, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 633.6019534827966 807.0318231425946 rotation: 42.281761200043334 positioned at: 640.0574941785022 785.2349172299989
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 43.21871420357605 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 551.8639213234806, y: 794.1963427245971, width: 99.31121985303889, height: 96.23231455080577, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 634.4687621126246 807.2480910286118 rotation: 43.21871420357605 positioned at: 641.1644654806358 785.6731910544972
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:8465 Saved to history: Rotate text History length: 3
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 551.8639213234806, y: 794.1963427245971, width: 99.31121985303889, height: 96.23231455080577, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 634.4687621126246 807.2480910286118 rotation: 43.21871420357605 positioned at: 641.1644654806358 785.6731910544972
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:5582 Stopped drawing rotation
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 43.21871420357605}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 43.21871420357605, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:5015 Drawing rotation handle clicked
9:4817 getDrawingBounds called for: text points: [{…}]
9:5527 Starting drawing rotation from angle: -0.8158950393524285
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 44.37591205764801 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 552.3474375066154, y: 793.6798422216007, width: 98.34418748676921, height: 97.26531555679867, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 635.5314080446168 807.5514925419484 rotation: 44.37591205764801 positioned at: 642.5186661334492 786.2561572483503
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 44.95486371624914 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 552.5953523194715, y: 793.4273357853112, width: 97.84835786105691, height: 97.77032842937751, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 636.0593205385277 807.7182485093088 rotation: 44.95486371624914 positioned at: 643.1903109293298 786.564976429148
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 45.52235174376465 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 552.8422154060323, y: 793.183682105792, width: 97.35463168793535, height: 98.25763578841611, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 636.5741134419711 807.891328970696 rotation: 45.52235174376465 positioned at: 643.8445899950427 786.8787096956199
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 46.100854753621775 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 553.0977753487878, y: 792.9392502279445, width: 96.84351180242447, height: 98.74649954411097, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 637.0959919908523 808.0775290753976 rotation: 46.100854753621775 positioned at: 644.5072211098998 787.2097074988731
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 46.656667747232945 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 553.3470015014442, y: 792.7081859537568, width: 96.3450594971116, height: 99.20862809248638, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 637.5944606921212 808.2656581825231 rotation: 46.656667747232945 positioned at: 645.1395381257051 787.538286895527
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 48.32222487785831 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 554.1152515998257, y: 792.0381778050822, width: 94.80855930034863, height: 100.54864438983554, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 639.0692298040766 808.8830577274107 rotation: 48.32222487785831 positioned at: 647.0071531473266 788.5842440137297
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 48.92044651223874 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 554.3989348329713, y: 791.805807447944, width: 94.2411928340573, height: 101.0133851041121, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 639.5913717772022 809.1242347332043 rotation: 48.92044651223874 positioned at: 647.6673304684043 788.9821089713206
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 49.440611570585546 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 554.6488929277375, y: 791.6073451916525, width: 93.74127664452499, height: 101.41030961669503, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 640.0418796465796 809.342189140819 rotation: 49.440611570585546 positioned at: 648.2365274263503 789.3374715795374
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 49.949958281090076 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 554.896597297236, y: 791.4162614203366, width: 93.24586790552803, height: 101.79247715932684, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 640.4797236667035 809.5629884248413 rotation: 49.949958281090076 positioned at: 648.7893785661407 789.6938599827074
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 51.05232113022259 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 555.4425799282453, y: 791.0137912798508, width: 92.15390264350935, height: 102.59741744029839, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 641.4155792894044 810.0655952308249 rotation: 51.05232113022259 positioned at: 649.9699856418333 790.4933860763637
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 51.53860564299811 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 555.6876858099226, y: 790.8411005160123, width: 91.66369088015472, height: 102.94279896797548, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 641.823048740671 810.2979495155807 rotation: 51.53860564299811 positioned at: 650.483597276255 790.8582059665915
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 52.15018005471994 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 555.9996046600511, y: 790.6281589202149, width: 91.03985317989782, height: 103.3686821595702, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 642.330612523476 810.599304668383 rotation: 52.15018005471994 positioned at: 651.1230480739171 791.327427086482
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 52.62371287820522 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 556.2439003764632, y: 790.4665424695495, width: 90.55126174707357, height: 103.69191506090101, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 642.7197470896332 810.8395667554339 rotation: 52.62371287820522 positioned at: 651.6130656055215 791.6986266537563
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 53.08720450732474 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 556.4853493566159, y: 790.311121013552, width: 90.06836378676826, height: 104.002757972896, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 643.0972624513319 811.0805342494747 rotation: 53.08720450732474 positioned at: 652.0882734636234 792.0685582752742
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 53.54091220338427 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 556.7239221703344, y: 790.1616424190149, width: 89.59121815933122, height: 104.30171516197015, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 643.4634954977963 811.3219238580215 rotation: 53.54091220338427 positioned at: 652.5491233065669 792.436951077333
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 54.15400653656339 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 557.0497685836624, y: 789.9638529442487, width: 88.9395253326752, height: 104.69729411150252, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 643.9530299554964 811.6566843008826 rotation: 54.15400653656339 positioned at: 653.1649097621721 792.9445131471682
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 55.21442212953671 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 557.6226516613285, y: 789.6332127947795, width: 87.79375917734296, height: 105.35857441044095, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 644.7846526221897 812.2586055252434 rotation: 55.21442212953671 positioned at: 654.2105094124091 793.8484684223336
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 55.64269633667658 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 557.8573269204717, y: 789.5038139917039, width: 87.32440865905664, height: 105.6173720165923, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 645.1149117174159 812.5098135468618 rotation: 55.64269633667658 positioned at: 654.6255972741696 794.2227739683893
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 56.26778896972564 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 558.2032222099598, y: 789.3192435853529, width: 86.63261808008042, height: 105.98651282929427, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 645.5909523662494 812.8847058892832 rotation: 56.26778896972564 positioned at: 655.2237936362447 794.7784647930733
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 57.313472552192906 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 558.7906961797017, y: 789.0219368431253, width: 85.45767014059652, height: 106.58112631374934, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 646.3708831066143 813.5333346505491 rotation: 57.313472552192906 positioned at: 656.203638926658 795.7324860032072
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 58.99470285598205 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 559.7579941579619, y: 788.5742005614187, width: 83.52307418407622, height: 107.47659887716259, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 647.5796852915222 814.6309712087306 rotation: 58.99470285598205 positioned at: 657.7220789000752 797.3286248075731
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 59.37940186889321 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 559.9832062242369, y: 788.477030391221, width: 83.07265005152613, height: 107.67093921755804, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 647.8481274369095 814.8913591895223 rotation: 59.37940186889321 positioned at: 658.0593068038132 797.7043427077319
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 60.02721125322198 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 560.3656569128448, y: 788.3178697395175, width: 82.30774867431046, height: 107.98926052096499, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 648.2930691753626 815.3373957942536 rotation: 60.02721125322198 positioned at: 658.6183245571353 798.3456228806833
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 60.683568236924444 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 560.7572153484174, y: 788.1623448975304, width: 81.52463180316522, height: 108.30031020493925, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 648.7346157397774 815.7988225062102 rotation: 60.683568236924444 positioned at: 659.1731934873134 799.0061718873459
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 61.04980311561461 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 560.9774556120551, y: 788.0780820179431, width: 81.08415127588978, height: 108.46883596411385, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 648.9768590401102 816.0603737456469 rotation: 61.04980311561461 positioned at: 659.4776747788579 799.3793898866375
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 61.70935552507066 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 561.3772274509723, y: 787.9308934318096, width: 80.28460759805535, height: 108.76321313638073, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 649.4055133964475 816.5386402423395 rotation: 61.70935552507066 positioned at: 660.0166084590409 800.0597515576729
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 62.37717113203903 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 561.7860771317565, y: 787.78785286224, width: 79.46690823648692, height: 109.04929427551997, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 649.8293998782193 817.0321966271877 rotation: 62.37717113203903 positioned at: 660.5497781527129 800.7592147007058
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 63.05322134519418 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 562.204082302841, y: 787.6492096432365, width: 78.63089789431797, height: 109.32658071352694, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 650.2479104240094 817.5411379925972 rotation: 63.05322134519418 positioned at: 661.0764673425485 801.4778848127279
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 63.39442109890936 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 562.416601176882, y: 787.581596865269, width: 78.20586014623609, height: 109.46180626946193, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 650.4550137627933 817.8014757622535 rotation: 63.39442109890936 positioned at: 661.3372264470346 801.8445527685262
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 67.4568738443562 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 565.0240886221504, y: 786.899068650671, width: 72.99088525569914, height: 110.82686269865803, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 652.6988903315868 821.0681695114955 rotation: 67.4568738443562 positioned at: 664.1707148821906 806.40059507288
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 69.84837766294127 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 566.6222055233055, y: 786.6038169466797, width: 69.79465145338895, height: 111.41736610664066, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 653.817964962223 823.1205433831378 rotation: 69.84837766294127 positioned at: 665.5936497732125 809.2304301853468
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 70.10587581842448 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 566.7969144235328, y: 786.5767668235293, width: 69.44523365293435, height: 111.47146635294143, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 653.9291246827801 823.3465708392804 rotation: 70.10587581842448 positioned at: 665.7355852587415 809.5409084464777
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 70.81310180653345 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 567.2793158612786, y: 786.507227169845, width: 68.48043077744273, height: 111.61054566030998, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 654.2249075259083 823.9720586949779 rotation: 70.81310180653345 positioned at: 666.113938473331 810.3990449366395
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 71.52644378010245 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 567.7696300586116, y: 786.4441557704945, width: 67.4998023827768, height: 111.73668845901102, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 654.5089797094092 824.6096686187173 rotation: 71.52644378010245 positioned at: 666.478379712291 811.2723297820551
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 72.24575744562856 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 568.2677763715802, y: 786.3877557325793, width: 66.50350975683955, height: 111.84948853484138, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 654.7807601869879 825.2590981839938 rotation: 72.24575744562856 positioned at: 666.8282246446897 812.1604017878296
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 72.47366433085767 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 568.426376989294, y: 786.3713955803877, width: 66.186308521412, height: 111.88220883922463, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 654.8637730944974 825.4661643236063 rotation: 72.47366433085767 positioned at: 666.93534574146 812.4432815610388
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 73.19059641568403 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 568.9276615259774, y: 786.3246720157764, width: 65.18373944804512, height: 111.9756559684472, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 655.115113992215 826.1214409576336 rotation: 73.19059641568403 positioned at: 667.2605549467444 813.3376627239329
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 74.11900175131774 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 569.582054387111, y: 786.274866628428, width: 63.874953725777914, height: 112.07526674314408, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 655.4183265828624 826.9783480654152 rotation: 74.11900175131774 positioned at: 667.6549688498449 814.5055430906198
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 74.838239327239 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 570.0929823173401, y: 786.2445910137772, width: 62.853097865319796, height: 112.1358179724457, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 655.6357872594564 827.648215032558 rotation: 74.838239327239 positioned at: 667.9396125627164 815.4173137085777
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 75.56239163241713 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 570.6108123835003, y: 786.2214423619108, width: 61.81743773299945, height: 112.18211527617837, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 655.8392173001928 828.3275633048291 rotation: 75.56239163241713 positioned at: 668.2076115995744 816.3410512709843
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 75.74665016882483 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 570.7431091295421, y: 786.2167271523766, width: 61.552844240915874, height: 112.19154569524676, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 655.8884808027981 828.5011620525937 rotation: 75.74665016882483 positioned at: 668.2728111338853 816.576962867417
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 76.46693641419718 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 571.262320163592, y: 786.2028695400612, width: 60.51442217281601, height: 112.21926091987757, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.0712803199566 829.1824846245564 rotation: 76.46693641419718 positioned at: 668.5159812109758 817.5023556456931
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 77.19159635032347 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 571.7879143703522, y: 786.1962813269851, width: 59.46323375929569, height: 112.23243734602988, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.2394015801221 829.8720100895403 rotation: 77.19159635032347 positioned at: 668.7417385600854 818.438179655259
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 78.07442938974904 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 572.432487768454, y: 786.1982229231944, width: 58.174086963091895, height: 112.22855415361119, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.4226860575434 830.7169972497337 rotation: 78.07442938974904 positioned at: 668.9910232678385 819.5841799510647
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 78.22458967744147 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 572.5425778424357, y: 786.1996426611934, width: 57.953906815128676, height: 112.22571467761327, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.4514970048805 830.8612163588574 rotation: 78.22458967744147 positioned at: 669.0305982519277 819.7796977250797
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 78.9398722174943 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 573.06876370408, y: 786.2107530249049, width: 56.90153509184006, height: 112.20349395019025, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.5792669685233 831.5499948890151 rotation: 78.9398722174943 positioned at: 669.20779502237 820.7132141499901
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 79.65865153986927 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 573.6004184375834, y: 786.2291553887494, width: 55.838225624833285, height: 112.16668922250119, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.6918437730925 832.2448551096345 rotation: 79.65865153986927 positioned at: 669.3669586955448 821.6546109970014
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 80.38072551179444 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 574.1373494415815, y: 786.2549444782641, width: 54.764363616837045, height: 112.11511104347187, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.788910798881 832.945258391381 rotation: 80.38072551179444 positioned at: 669.5077083934178 822.6032497998765
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 80.50140718483865 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 574.2273593706929, y: 786.2599682374315, width: 54.584343758614295, height: 112.10506352513698, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.8035634533522 833.0625216036759 rotation: 80.50140718483865 positioned at: 669.5293565495601 822.7620539511154
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 81.2177375125308 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 574.7631969855304, y: 786.2939925070999, width: 53.51266852893923, height: 112.03701498580017, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.8812606831975 833.7596001468778 rotation: 81.2177375125308 positioned at: 669.6467745859784 823.7060022429329
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 81.93683466054746 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 575.3037384703653, y: 786.3353828856974, width: 52.43158555926948, height: 111.95423422860517, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.9432606313096 834.4608847588089 rotation: 81.93683466054746 positioned at: 669.745541362608 824.6555901506001
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 82.65848690239977 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 575.848768377394, y: 786.3842010915472, width: 51.34152574521204, height: 111.8565978169056, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.9893366096746 835.1658006112575 rotation: 82.65848690239977 positioned at: 669.8253789861723 825.6101357588576
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 82.75002921773188 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 575.9180854488968, y: 786.3909146378584, width: 51.202891602206364, height: 111.84317072428325, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 656.9940243219859 835.2552818638303 rotation: 82.75002921773188 positioned at: 669.8341246026698 825.7313125984856
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 83.46511672418 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 576.4609161119618, y: 786.4473908169111, width: 50.1172302760765, height: 111.73021836617772, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0216708663197 835.9545976691217 rotation: 83.46511672418 positioned at: 669.8917258712855 826.6784355264715
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 84.18224391482842 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 577.0076506778295, y: 786.5112028116827, width: 49.02376114434105, height: 111.60259437663467, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.03341512379 836.6562233849218 rotation: 84.18224391482842 positioned at: 669.9304019936266 827.6289312250318
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 84.90119395075057 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 577.5580569187903, y: 786.5823793347226, width: 47.92294866241946, height: 111.46024133055471, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.029123236028 837.3595668376137 rotation: 84.90119395075057 positioned at: 669.9499815881662 828.5820960181499
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 85.62174659456598 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 578.1118956116769, y: 786.6609388713738, width: 46.81527127664617, height: 111.30312225725243, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0086899423991 838.064026902171 rotation: 85.62174659456598 positioned at: 669.9503259242355 829.5372150709595
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 85.67585965319314 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 578.1535752506286, y: 786.6671302742006, width: 46.73191199874282, height: 111.29073945159871, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0065038901043 838.1169024076163 rotation: 85.67585965319314 positioned at: 669.9495729769033 829.6089251534231
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 85.67585965319314 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 578.1535752506286, y: 786.6671302742006, width: 46.73191199874282, height: 111.29073945159871, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0065038901043 838.1169024076163 rotation: 85.67585965319314 positioned at: 669.9495729769033 829.6089251534231
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 84.96419692212227 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 577.6063960917878, y: 786.5889599744977, width: 47.82627031642437, height: 111.4470800510046, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0279807280174 837.4211863195919 rotation: 84.96419692212227 positioned at: 669.9507815596277 828.665621731208
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4817 getDrawingBounds called for: text points: [{…}]
9:5558 Rotating drawing to: 84.25407803875781 degrees
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 577.0625433764803, y: 786.5179903405482, width: 48.91397574703933, height: 111.58901931890364, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0337095771713 836.726507447402 rotation: 84.25407803875781 positioned at: 669.9332225098983 827.724162561967
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:8465 Saved to history: Rotate text History length: 4
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 577.0625433764803, y: 786.5179903405482, width: 48.91397574703933, height: 111.58901931890364, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0337095771713 836.726507447402 rotation: 84.25407803875781 positioned at: 669.9332225098983 827.724162561967
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:5582 Stopped drawing rotation
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:5082 Removed drawing handles
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
notification-center.js?v=1759155476:148 [NotificationCenter] Desktop bell reset to normal
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759155476:177
init @ notification-center.js?v=1759155476:45
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759155476:77
setInterval
init @ notifications.js?v=1759155476:76
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: true
9:5082 Removed drawing handles
9:4693 Drawing canvas rect: DOMRect {x: 673.5, y: -8.3125, width: 2000, height: 1400, top: -8.3125, …}
9:4817 getDrawingBounds called for: text points: [{…}]
9:4702 Drawing bounds for all types: {x: 577.0625433764803, y: 786.5179903405482, width: 48.91397574703933, height: 111.58901931890364, centerX: 601.51953125, …}
9:4741 Using text handles: all 8 handles
9:5009 Rotation handle orbiting around n handle at: 657.0337095771713 836.726507447402 rotation: 84.25407803875781 positioned at: 669.9332225098983 827.724162561967
9:4770 Drawing handles added to canvas container (like grid elements)
9:4809 Added drawing drag area for canvas drawing movement
9:4450 showGeneralDrawingEditor called for: text draw_4
9:4463 Creating editor for: Text
9:4522 General drawing editor created and added to DOM
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}clickPoint: x: 646.5y: 867.3125[[Prototype]]: Objectrotation: 84.25407803875781textPosition: x: 556.5y: 832.3125[[Prototype]]: ObjecttextSize: height: 20width: 90.0390625[[Prototype]]: Object[[Prototype]]: Object
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: 84.25407803875781}
9:6087 🔄 Rotation transform: {center: {…}, rotation: 84.25407803875781, transformed: {…}}
9:6099 🎯 Hit result: false
9:6065 🎯 Text hit test: {clickPoint: {…}, textPosition: {…}, textSize: {…}, rotation: undefined}
9:6108 🎯 Hit result (no rotation): false
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759155476:148 [NotificationCenter] Desktop bell reset to normal
notification-center.js?v=1759155476:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759155476:177
init @ notification-center.js?v=1759155476:45
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
notifications.js?v=1759155476:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759155476:77
setInterval
init @ notifications.js?v=1759155476:76
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notification-center.js?v=1759155476:148 [NotificationCenter] Desktop bell reset to normal
notification-center.js?v=1759155476:69 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759155476:177
init @ notification-center.js?v=1759155476:45
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
notifications.js?v=1759155476:554 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759155476:77
setInterval
init @ notifications.js?v=1759155476:76
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
 🖥️ Desktop: Enhanced loadUnreadNotifications called
 [NotificationCenter] Desktop bell reset to normal
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759155476:177
init @ notification-center.js?v=1759155476:45
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759155476:77
setInterval
init @ notifications.js?v=1759155476:76
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
 🖥️ Desktop: Enhanced loadUnreadNotifications called
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759155476:177
init @ notification-center.js?v=1759155476:45
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
 [NotificationCenter] Desktop bell reset to normal
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759155476:77
setInterval
init @ notifications.js?v=1759155476:76
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
 🖥️ Desktop: Enhanced loadUnreadNotifications called
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification_center/getUnreadCount".
updateNotificationCount @ notification-center.js?v=1759155476:69
(anonymous) @ notification-center.js?v=1759155476:178
setInterval
startPeriodicUpdates @ notification-center.js?v=1759155476:177
init @ notification-center.js?v=1759155476:45
NotificationCenter @ notification-center.js?v=1759155476:15
(anonymous) @ notification-center.js?v=1759155476:362
 [NotificationCenter] Desktop bell reset to normal
 Fetch finished loading: GET "https://events.rowaneliterides.com/notification/getUnread".
loadUnreadNotifications @ notifications.js?v=1759155476:554
manager.loadUnreadNotifications @ desktop-notifications-fix.js:167
(anonymous) @ notifications.js?v=1759155476:77
setInterval
init @ notifications.js?v=1759155476:76
NotificationManager @ notifications.js?v=1759155476:46
(anonymous) @ notifications.js?v=1759155476:1160
